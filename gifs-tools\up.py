from PIL import Image
import os
import glob

def upscale_gif(input_path, output_path, target_size):
    """
    将 GIF 无损放大到指定尺寸（保持像素风格）
    :param input_path: 输入 GIF 路径
    :param output_path: 输出 GIF 路径
    :param target_size: 目标尺寸，如 (100, 100)
    """
    with Image.open(input_path) as im:
        frames = []
        durations = []
        disposals = []

        try:
            while True:
                # 获取当前帧和相关参数
                frame = im.convert("RGBA")
                duration = im.info.get('duration', 100)  # 默认 100ms
                disposal = im.disposal_method if hasattr(im, 'disposal_method') else 0

                # 放大帧（使用 NEAREST 保持像素清晰）
                resized = frame.resize(target_size, Image.NEAREST)
                frames.append(resized)
                durations.append(duration)
                disposals.append(disposal)

                im.seek(im.tell() + 1)
        except EOFError:
            pass  # 所有帧处理完毕

        # 保存新 GIF
        frames[0].save(
            output_path,
            save_all=True,
            append_images=frames[1:],
            duration=durations,
            loop=0,  # 0 表示无限循环
            disposal=disposals,
            optimize=False
        )

def batch_upscale_gifs(input_dir=".", output_dir="upscaled", target_size=(100, 100)):
    """
    批量处理目录中的所有GIF文件
    :param input_dir: 输入目录路径
    :param output_dir: 输出目录路径
    :param target_size: 目标尺寸，如 (100, 100)
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    # 查找所有GIF文件
    gif_pattern = os.path.join(input_dir, "*.gif")
    gif_files = glob.glob(gif_pattern)
    
    if not gif_files:
        print(f"在目录 {input_dir} 中未找到GIF文件")
        return
    
    print(f"找到 {len(gif_files)} 个GIF文件:")
    for gif_file in gif_files:
        print(f"  - {os.path.basename(gif_file)}")
    
    # 批量处理
    for i, input_gif in enumerate(gif_files, 1):
        filename = os.path.basename(input_gif)
        name_without_ext = os.path.splitext(filename)[0]
        output_gif = os.path.join(output_dir, f"{name_without_ext}_{target_size[0]}x{target_size[1]}.gif")
        
        print(f"\n处理文件 {i}/{len(gif_files)}: {filename}")
        try:
            upscale_gif(input_gif, output_gif, target_size)
            print(f"✓ 成功转换: {output_gif}")
        except Exception as e:
            print(f"✗ 转换失败: {filename} - {str(e)}")
    
    print(f"\n批量转换完成！共处理 {len(gif_files)} 个文件")

# 批量处理示例
if __name__ == "__main__":
    # 处理当前目录中的所有GIF文件，输出到upscaled目录
    batch_upscale_gifs(input_dir=".", output_dir="upscaled", target_size=(100, 100))
    
    # 也可以指定不同的尺寸
    # batch_upscale_gifs(input_dir=".", output_dir="upscaled_200x200", target_size=(200, 200))