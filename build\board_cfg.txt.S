/* * Data converted from E:/APPprj/LinkPet/linkpet-esp/components/codec_board/board_cfg.txt
 * (null byte appended)
 */
.data
#if !defined (__APPLE__) && !defined (__linux__)
.section .rodata.embedded
#endif

.global board_cfg_txt
board_cfg_txt:

.global _binary_board_cfg_txt_start
_binary_board_cfg_txt_start: /* for objcopy compatibility */
.byte 0x23, 0x20, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x20, 0x69, 0x6e, 0x2c, 0x20, 0x6f, 0x75
.byte 0x74, 0x2c, 0x20, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x20, 0x74, 0x79, 0x70, 0x65, 0x0a, 0x23
.byte 0x20, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x20, 0x69, 0x32, 0x63, 0x5f, 0x70, 0x6f, 0x72
.byte 0x74, 0x2c, 0x20, 0x69, 0x32, 0x73, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x20, 0x73, 0x65, 0x74, 0x74
.byte 0x69, 0x6e, 0x67, 0x73, 0x0a, 0x23, 0x20, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x20, 0x70
.byte 0x61, 0x5f, 0x67, 0x61, 0x69, 0x6e, 0x2c, 0x20, 0x69, 0x32, 0x63, 0x5f, 0x61, 0x64, 0x64, 0x72
.byte 0x20, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x0a, 0x0a, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x3a
.byte 0x20, 0x43, 0x36, 0x5f, 0x41, 0x4d, 0x4f, 0x4c, 0x45, 0x44, 0x5f, 0x31, 0x5f, 0x34, 0x33, 0x0a
.byte 0x69, 0x32, 0x63, 0x3a, 0x20, 0x7b, 0x73, 0x64, 0x61, 0x3a, 0x20, 0x31, 0x38, 0x2c, 0x20, 0x73
.byte 0x63, 0x6c, 0x3a, 0x20, 0x38, 0x7d, 0x0a, 0x69, 0x32, 0x73, 0x3a, 0x20, 0x7b, 0x62, 0x63, 0x6c
.byte 0x6b, 0x3a, 0x20, 0x32, 0x31, 0x2c, 0x20, 0x77, 0x73, 0x3a, 0x20, 0x32, 0x32, 0x2c, 0x20, 0x64
.byte 0x6f, 0x75, 0x74, 0x3a, 0x20, 0x32, 0x33, 0x2c, 0x20, 0x64, 0x69, 0x6e, 0x3a, 0x20, 0x32, 0x30
.byte 0x2c, 0x20, 0x6d, 0x63, 0x6c, 0x6b, 0x3a, 0x20, 0x31, 0x39, 0x7d, 0x0a, 0x6f, 0x75, 0x74, 0x3a
.byte 0x20, 0x7b, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x3a, 0x20, 0x45, 0x53, 0x38, 0x33, 0x31, 0x31, 0x2c
.byte 0x20, 0x70, 0x61, 0x3a, 0x20, 0x2d, 0x31, 0x2c, 0x20, 0x75, 0x73, 0x65, 0x5f, 0x6d, 0x63, 0x6c
.byte 0x6b, 0x3a, 0x20, 0x31, 0x2c, 0x20, 0x70, 0x61, 0x5f, 0x67, 0x61, 0x69, 0x6e, 0x3a, 0x36, 0x7d
.byte 0x0a, 0x69, 0x6e, 0x3a, 0x20, 0x7b, 0x63, 0x6f, 0x64, 0x65, 0x63, 0x3a, 0x20, 0x45, 0x53, 0x37
.byte 0x32, 0x31, 0x30, 0x7d, 0x00

.global _binary_board_cfg_txt_end
_binary_board_cfg_txt_end: /* for objcopy compatibility */


.global board_cfg_txt_length
board_cfg_txt_length: /* not including null byte */
.long 276
