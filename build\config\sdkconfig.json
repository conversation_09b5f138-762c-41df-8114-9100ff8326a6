{"ADC_CALI_SUPPRESS_DEPRECATE_WARN": false, "ADC_CONTINUOUS_ISR_IRAM_SAFE": false, "ADC_ENABLE_DEBUG_LOG": false, "ADC_ONESHOT_CTRL_FUNC_IN_IRAM": false, "ADC_SUPPRESS_DEPRECATE_WARN": false, "APPTRACE_DEST_JTAG": false, "APPTRACE_DEST_NONE": true, "APPTRACE_DEST_UART1": false, "APPTRACE_DEST_UART2": false, "APPTRACE_DEST_UART_NONE": true, "APPTRACE_LOCK_ENABLE": true, "APPTRACE_UART_TASK_PRIO": 1, "APP_BUILD_BOOTLOADER": true, "APP_BUILD_GENERATE_BINARIES": true, "APP_BUILD_TYPE_APP_2NDBOOT": true, "APP_BUILD_TYPE_RAM": false, "APP_BUILD_USE_FLASH_SECTIONS": true, "APP_COMPILE_TIME_DATE": true, "APP_EXCLUDE_PROJECT_NAME_VAR": false, "APP_EXCLUDE_PROJECT_VER_VAR": false, "APP_NO_BLOBS": false, "APP_PROJECT_VER_FROM_CONFIG": false, "APP_REPRODUCIBLE_BUILD": false, "APP_RETRIEVE_LEN_ELF_SHA": 9, "BLE_MESH": false, "BOOTLOADER_APP_ROLLBACK_ENABLE": false, "BOOTLOADER_APP_TEST": false, "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG": false, "BOOTLOADER_COMPILER_OPTIMIZATION_PERF": false, "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE": true, "BOOTLOADER_COMPILE_TIME_DATE": true, "BOOTLOADER_CUSTOM_RESERVE_RTC": false, "BOOTLOADER_FACTORY_RESET": false, "BOOTLOADER_FLASH_DC_AWARE": false, "BOOTLOADER_FLASH_XMC_SUPPORT": true, "BOOTLOADER_LOG_LEVEL": 3, "BOOTLOADER_LOG_LEVEL_DEBUG": false, "BOOTLOADER_LOG_LEVEL_ERROR": false, "BOOTLOADER_LOG_LEVEL_INFO": true, "BOOTLOADER_LOG_LEVEL_NONE": false, "BOOTLOADER_LOG_LEVEL_VERBOSE": false, "BOOTLOADER_LOG_LEVEL_WARN": false, "BOOTLOADER_OFFSET_IN_FLASH": 0, "BOOTLOADER_PROJECT_VER": 1, "BOOTLOADER_REGION_PROTECTION_ENABLE": true, "BOOTLOADER_RESERVE_RTC_SIZE": 0, "BOOTLOADER_SKIP_VALIDATE_ALWAYS": false, "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP": false, "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON": false, "BOOTLOADER_WDT_DISABLE_IN_USER_CODE": false, "BOOTLOADER_WDT_ENABLE": true, "BOOTLOADER_WDT_TIME_MS": 9000, "BOOT_ROM_LOG_ALWAYS_OFF": false, "BOOT_ROM_LOG_ALWAYS_ON": true, "BOOT_ROM_LOG_ON_GPIO_HIGH": false, "BOOT_ROM_LOG_ON_GPIO_LOW": false, "BT_ALARM_MAX_NUM": 50, "BT_BLUEDROID_ENABLED": false, "BT_CONTROLLER_DISABLED": false, "BT_CONTROLLER_ENABLED": true, "BT_CONTROLLER_ONLY": false, "BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD": 20, "BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM": 100, "BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP": true, "BT_ENABLED": true, "BT_HCI_LOG_DEBUG_EN": false, "BT_LE_COEX_PHY_CODED_TX_RX_TLIM_DIS": true, "BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EFF": 0, "BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EN": false, "BT_LE_CONTROLLER_LOG_ENABLED": false, "BT_LE_CONTROLLER_NPL_OS_PORTING_SUPPORT": true, "BT_LE_CONTROLLER_TASK_STACK_SIZE": 4096, "BT_LE_DFT_TX_POWER_LEVEL_DBM_EFF": 9, "BT_LE_DFT_TX_POWER_LEVEL_N0": false, "BT_LE_DFT_TX_POWER_LEVEL_N12": false, "BT_LE_DFT_TX_POWER_LEVEL_N15": false, "BT_LE_DFT_TX_POWER_LEVEL_N3": false, "BT_LE_DFT_TX_POWER_LEVEL_N6": false, "BT_LE_DFT_TX_POWER_LEVEL_N9": false, "BT_LE_DFT_TX_POWER_LEVEL_P12": false, "BT_LE_DFT_TX_POWER_LEVEL_P15": false, "BT_LE_DFT_TX_POWER_LEVEL_P18": false, "BT_LE_DFT_TX_POWER_LEVEL_P20": false, "BT_LE_DFT_TX_POWER_LEVEL_P3": false, "BT_LE_DFT_TX_POWER_LEVEL_P6": false, "BT_LE_DFT_TX_POWER_LEVEL_P9": true, "BT_LE_HCI_INTERFACE_USE_RAM": true, "BT_LE_HCI_INTERFACE_USE_UART": false, "BT_LE_LL_DUP_SCAN_LIST_COUNT": 20, "BT_LE_LL_RESOLV_LIST_SIZE": 4, "BT_LE_LL_SCA": 60, "BT_LE_LP_CLK_SRC_DEFAULT": false, "BT_LE_LP_CLK_SRC_MAIN_XTAL": true, "BT_LE_MSYS_INIT_IN_CONTROLLER": true, "BT_LE_SCAN_DUPL": true, "BT_LE_SCAN_DUPL_CACHE_REFRESH_PERIOD": 0, "BT_LE_SCAN_DUPL_TYPE": 0, "BT_LE_SCAN_DUPL_TYPE_DATA": false, "BT_LE_SCAN_DUPL_TYPE_DATA_DEVICE": false, "BT_LE_SCAN_DUPL_TYPE_DEVICE": true, "BT_LE_SLEEP_ENABLE": false, "BT_LE_TX_CCA_ENABLED": false, "BT_NIMBLE_50_FEATURE_SUPPORT": true, "BT_NIMBLE_ATT_PREFERRED_MTU": 256, "BT_NIMBLE_BLE_GATT_BLOB_TRANSFER": false, "BT_NIMBLE_BLE_POWER_CONTROL": false, "BT_NIMBLE_BLUFI_ENABLE": false, "BT_NIMBLE_CRYPTO_STACK_MBEDTLS": true, "BT_NIMBLE_DEBUG": false, "BT_NIMBLE_DYNAMIC_SERVICE": false, "BT_NIMBLE_ENABLED": true, "BT_NIMBLE_ENABLE_CONN_REATTEMPT": true, "BT_NIMBLE_ENC_ADV_DATA": false, "BT_NIMBLE_EXT_ADV": false, "BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN": 31, "BT_NIMBLE_GATT_CACHING": false, "BT_NIMBLE_GATT_MAX_PROCS": 4, "BT_NIMBLE_HCI_UART_CTS_PIN": 23, "BT_NIMBLE_HCI_UART_FLOW_CTRL": 0, "BT_NIMBLE_HCI_UART_RTS_PIN": 19, "BT_NIMBLE_HID_SERVICE": false, "BT_NIMBLE_HIGH_DUTY_ADV_ITVL": false, "BT_NIMBLE_HOST_QUEUE_CONG_CHECK": false, "BT_NIMBLE_HOST_TASK_STACK_SIZE": 4096, "BT_NIMBLE_HS_FLOW_CTRL": false, "BT_NIMBLE_HS_STOP_TIMEOUT_MS": 2000, "BT_NIMBLE_L2CAP_COC_MAX_NUM": 0, "BT_NIMBLE_L2CAP_COC_SDU_BUFF_COUNT": 1, "BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY": true, "BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY": true, "BT_NIMBLE_LL_CFG_FEAT_LE_ENCRYPTION": true, "BT_NIMBLE_LOG_LEVEL": 1, "BT_NIMBLE_LOG_LEVEL_DEBUG": false, "BT_NIMBLE_LOG_LEVEL_ERROR": false, "BT_NIMBLE_LOG_LEVEL_INFO": true, "BT_NIMBLE_LOG_LEVEL_NONE": false, "BT_NIMBLE_LOG_LEVEL_WARNING": false, "BT_NIMBLE_MAX_BONDS": 3, "BT_NIMBLE_MAX_CCCDS": 8, "BT_NIMBLE_MAX_CONNECTIONS": 8, "BT_NIMBLE_MAX_CONN_REATTEMPT": 3, "BT_NIMBLE_MAX_PERIODIC_ADVERTISER_LIST": 5, "BT_NIMBLE_MAX_PERIODIC_SYNCS": 0, "BT_NIMBLE_MEM_ALLOC_MODE_DEFAULT": false, "BT_NIMBLE_MEM_ALLOC_MODE_INTERNAL": true, "BT_NIMBLE_MESH": false, "BT_NIMBLE_MSYS_1_BLOCK_COUNT": 24, "BT_NIMBLE_MSYS_1_BLOCK_SIZE": 128, "BT_NIMBLE_MSYS_2_BLOCK_COUNT": 24, "BT_NIMBLE_MSYS_2_BLOCK_SIZE": 320, "BT_NIMBLE_MSYS_BUF_FROM_HEAP": true, "BT_NIMBLE_NVS_PERSIST": false, "BT_NIMBLE_OPTIMIZE_MULTI_CONN": false, "BT_NIMBLE_PINNED_TO_CORE": 0, "BT_NIMBLE_ROLE_BROADCASTER": true, "BT_NIMBLE_ROLE_CENTRAL": true, "BT_NIMBLE_ROLE_OBSERVER": true, "BT_NIMBLE_ROLE_PERIPHERAL": true, "BT_NIMBLE_RPA_TIMEOUT": 900, "BT_NIMBLE_SECURITY_ENABLE": true, "BT_NIMBLE_SMP_ID_RESET": false, "BT_NIMBLE_SM_LEGACY": true, "BT_NIMBLE_SM_LVL": 0, "BT_NIMBLE_SM_SC": true, "BT_NIMBLE_SM_SC_DEBUG_KEYS": false, "BT_NIMBLE_SVC_GAP_APPEARANCE": 0, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM": 0, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHN": 0, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHR": 0, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ENC": 0, "BT_NIMBLE_SVC_GAP_CAR_CHAR_NOT_SUPP": true, "BT_NIMBLE_SVC_GAP_CAR_NOT_SUPP": false, "BT_NIMBLE_SVC_GAP_CAR_SUPP": false, "BT_NIMBLE_SVC_GAP_CENT_ADDR_RESOLUTION": -1, "BT_NIMBLE_SVC_GAP_DEVICE_NAME": "nimble", "BT_NIMBLE_SVC_GAP_NAME_WRITE": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM": 0, "BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHEN": 0, "BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHOR": 0, "BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_ENC": 0, "BT_NIMBLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL": 0, "BT_NIMBLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL": 0, "BT_NIMBLE_SVC_GAP_PPCP_SLAVE_LATENCY": 0, "BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO": 0, "BT_NIMBLE_TEST_THROUGHPUT_TEST": false, "BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT": 24, "BT_NIMBLE_TRANSPORT_ACL_SIZE": 255, "BT_NIMBLE_TRANSPORT_EVT_COUNT": 30, "BT_NIMBLE_TRANSPORT_EVT_DISCARD_COUNT": 8, "BT_NIMBLE_TRANSPORT_EVT_SIZE": 70, "BT_NIMBLE_USE_ESP_TIMER": true, "BT_NIMBLE_VS_SUPPORT": false, "BT_NIMBLE_WHITELIST_SIZE": 12, "CODEC_AW88298_SUPPORT": true, "CODEC_CJC8910_SUPPORT": false, "CODEC_ES7210_SUPPORT": true, "CODEC_ES7243E_SUPPORT": true, "CODEC_ES7243_SUPPORT": true, "CODEC_ES8156_SUPPORT": true, "CODEC_ES8311_SUPPORT": true, "CODEC_ES8374_SUPPORT": true, "CODEC_ES8388_SUPPORT": true, "CODEC_ES8389_SUPPORT": true, "CODEC_I2C_BACKWARD_COMPATIBLE": false, "CODEC_TAS5805M_SUPPORT": true, "CODEC_ZL38063_SUPPORT": false, "COMPILER_CXX_EXCEPTIONS": false, "COMPILER_CXX_RTTI": false, "COMPILER_DISABLE_GCC12_WARNINGS": false, "COMPILER_DISABLE_GCC13_WARNINGS": false, "COMPILER_DUMP_RTL_FILES": false, "COMPILER_FLOAT_LIB_FROM_GCCLIB": false, "COMPILER_FLOAT_LIB_FROM_RVFPLIB": true, "COMPILER_HIDE_PATHS_MACROS": true, "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE": false, "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE": true, "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT": false, "COMPILER_OPTIMIZATION_ASSERTION_LEVEL": 2, "COMPILER_OPTIMIZATION_CHECKS_SILENT": false, "COMPILER_OPTIMIZATION_DEBUG": false, "COMPILER_OPTIMIZATION_NONE": false, "COMPILER_OPTIMIZATION_PERF": true, "COMPILER_OPTIMIZATION_SIZE": false, "COMPILER_ORPHAN_SECTIONS_PLACE": true, "COMPILER_ORPHAN_SECTIONS_WARNING": false, "COMPILER_RT_LIB_GCCLIB": true, "COMPILER_RT_LIB_NAME": "gcc", "COMPILER_SAVE_RESTORE_LIBCALLS": false, "COMPILER_STACK_CHECK_MODE_ALL": false, "COMPILER_STACK_CHECK_MODE_NONE": true, "COMPILER_STACK_CHECK_MODE_NORM": false, "COMPILER_STACK_CHECK_MODE_STRONG": false, "COMPILER_WARN_WRITE_STRINGS": false, "CONSOLE_SORTED_HELP": false, "CU_DIAGNOSTICS_COLOR_ALWAYS": true, "CU_DIAGNOSTICS_COLOR_AUTO": false, "CU_DIAGNOSTICS_COLOR_NEVER": false, "CU_GCC_LTO_ENABLE": false, "CU_GCC_STRING_1BYTE_ALIGN": false, "CU_RELINKER_ENABLE": false, "EFUSE_CUSTOM_TABLE": false, "EFUSE_MAX_BLK_LEN": 256, "EFUSE_VIRTUAL": false, "ESP32C6_REV_MAX_FULL": 99, "ESP32C6_REV_MIN_0": true, "ESP32C6_REV_MIN_1": false, "ESP32C6_REV_MIN_FULL": 0, "ESP32C6_UNIVERSAL_MAC_ADDRESSES": 4, "ESP32C6_UNIVERSAL_MAC_ADDRESSES_FOUR": true, "ESP32C6_UNIVERSAL_MAC_ADDRESSES_TWO": false, "ESPTOOLPY_AFTER": "hard_reset", "ESPTOOLPY_AFTER_NORESET": false, "ESPTOOLPY_AFTER_RESET": true, "ESPTOOLPY_BEFORE": "default_reset", "ESPTOOLPY_BEFORE_NORESET": false, "ESPTOOLPY_BEFORE_RESET": true, "ESPTOOLPY_FLASHFREQ": "80m", "ESPTOOLPY_FLASHFREQ_20M": false, "ESPTOOLPY_FLASHFREQ_40M": false, "ESPTOOLPY_FLASHFREQ_80M": true, "ESPTOOLPY_FLASHFREQ_80M_DEFAULT": true, "ESPTOOLPY_FLASHMODE": "dio", "ESPTOOLPY_FLASHMODE_DIO": true, "ESPTOOLPY_FLASHMODE_DOUT": false, "ESPTOOLPY_FLASHMODE_QIO": false, "ESPTOOLPY_FLASHMODE_QOUT": false, "ESPTOOLPY_FLASHSIZE": "16MB", "ESPTOOLPY_FLASHSIZE_128MB": false, "ESPTOOLPY_FLASHSIZE_16MB": true, "ESPTOOLPY_FLASHSIZE_1MB": false, "ESPTOOLPY_FLASHSIZE_2MB": false, "ESPTOOLPY_FLASHSIZE_32MB": false, "ESPTOOLPY_FLASHSIZE_4MB": false, "ESPTOOLPY_FLASHSIZE_64MB": false, "ESPTOOLPY_FLASHSIZE_8MB": false, "ESPTOOLPY_FLASH_SAMPLE_MODE_STR": true, "ESPTOOLPY_HEADER_FLASHSIZE_UPDATE": false, "ESPTOOLPY_MONITOR_BAUD": 115200, "ESPTOOLPY_NO_STUB": false, "ESP_BROWNOUT_DET": true, "ESP_BROWNOUT_DET_LVL": 7, "ESP_BROWNOUT_DET_LVL_SEL_2": false, "ESP_BROWNOUT_DET_LVL_SEL_3": false, "ESP_BROWNOUT_DET_LVL_SEL_4": false, "ESP_BROWNOUT_DET_LVL_SEL_5": false, "ESP_BROWNOUT_DET_LVL_SEL_6": false, "ESP_BROWNOUT_DET_LVL_SEL_7": true, "ESP_COEX_ENABLED": true, "ESP_COEX_POWER_MANAGEMENT": false, "ESP_COEX_SW_COEXIST_ENABLE": true, "ESP_CONSOLE_NONE": false, "ESP_CONSOLE_ROM_SERIAL_PORT_NUM": 0, "ESP_CONSOLE_SECONDARY_NONE": false, "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG": true, "ESP_CONSOLE_UART": true, "ESP_CONSOLE_UART_BAUDRATE": 115200, "ESP_CONSOLE_UART_CUSTOM": false, "ESP_CONSOLE_UART_DEFAULT": true, "ESP_CONSOLE_UART_NUM": 0, "ESP_CONSOLE_USB_SERIAL_JTAG": false, "ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED": true, "ESP_COREDUMP_ENABLE_TO_FLASH": false, "ESP_COREDUMP_ENABLE_TO_NONE": true, "ESP_COREDUMP_ENABLE_TO_UART": false, "ESP_CRYPTO_DPA_PROTECTION_AT_STARTUP": true, "ESP_CRYPTO_DPA_PROTECTION_LEVEL": 1, "ESP_CRYPTO_DPA_PROTECTION_LEVEL_HIGH": false, "ESP_CRYPTO_DPA_PROTECTION_LEVEL_LOW": true, "ESP_CRYPTO_DPA_PROTECTION_LEVEL_MEDIUM": false, "ESP_DEBUG_OCDAWARE": true, "ESP_DEBUG_STUBS_ENABLE": false, "ESP_DEFAULT_CPU_FREQ_MHZ": 160, "ESP_DEFAULT_CPU_FREQ_MHZ_120": false, "ESP_DEFAULT_CPU_FREQ_MHZ_160": true, "ESP_DEFAULT_CPU_FREQ_MHZ_80": false, "ESP_ERR_TO_NAME_LOOKUP": true, "ESP_EVENT_LOOP_PROFILING": false, "ESP_EVENT_POST_FROM_IRAM_ISR": true, "ESP_EVENT_POST_FROM_ISR": true, "ESP_GDBSTUB_ENABLED": true, "ESP_GDBSTUB_MAX_TASKS": 32, "ESP_GDBSTUB_SUPPORT_TASKS": true, "ESP_HTTPS_OTA_ALLOW_HTTP": false, "ESP_HTTPS_OTA_DECRYPT_CB": false, "ESP_HTTPS_SERVER_ENABLE": false, "ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH": false, "ESP_HTTP_CLIENT_ENABLE_CUSTOM_TRANSPORT": false, "ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH": false, "ESP_HTTP_CLIENT_ENABLE_HTTPS": true, "ESP_INT_WDT": true, "ESP_INT_WDT_TIMEOUT_MS": 300, "ESP_IPC_TASK_STACK_SIZE": 1024, "ESP_MAC_ADDR_UNIVERSE_BT": true, "ESP_MAC_ADDR_UNIVERSE_ETH": true, "ESP_MAC_ADDR_UNIVERSE_IEEE802154": true, "ESP_MAC_ADDR_UNIVERSE_WIFI_AP": true, "ESP_MAC_ADDR_UNIVERSE_WIFI_STA": true, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES": 4, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR": true, "ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC": false, "ESP_MAIN_TASK_AFFINITY": 0, "ESP_MAIN_TASK_AFFINITY_CPU0": true, "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY": false, "ESP_MAIN_TASK_STACK_SIZE": 3584, "ESP_MINIMAL_SHARED_STACK_SIZE": 2048, "ESP_NETIF_BRIDGE_EN": false, "ESP_NETIF_IP_LOST_TIMER_INTERVAL": 120, "ESP_NETIF_L2_TAP": false, "ESP_NETIF_LOOPBACK": false, "ESP_NETIF_RECEIVE_REPORT_ERRORS": false, "ESP_NETIF_TCPIP_LWIP": true, "ESP_NETIF_USES_TCPIP_WITH_BSD_API": true, "ESP_PANIC_HANDLER_IRAM": false, "ESP_PHY_CALIBRATION_AND_DATA_STORAGE": true, "ESP_PHY_CALIBRATION_MODE": 0, "ESP_PHY_ENABLED": true, "ESP_PHY_INIT_DATA_IN_PARTITION": false, "ESP_PHY_MAX_TX_POWER": 20, "ESP_PHY_MAX_WIFI_TX_POWER": 20, "ESP_PHY_PLL_TRACK_DEBUG": false, "ESP_PHY_REDUCE_TX_POWER": false, "ESP_PHY_RF_CAL_FULL": false, "ESP_PHY_RF_CAL_NONE": false, "ESP_PHY_RF_CAL_PARTIAL": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2": true, "ESP_REV_MAX_FULL": 99, "ESP_REV_MIN_FULL": 0, "ESP_ROM_GET_CLK_FREQ": true, "ESP_ROM_HAS_CRC_BE": true, "ESP_ROM_HAS_CRC_LE": true, "ESP_ROM_HAS_HAL_SYSTIMER": true, "ESP_ROM_HAS_HAL_WDT": true, "ESP_ROM_HAS_HEAP_TLSF": true, "ESP_ROM_HAS_JPEG_DECODE": true, "ESP_ROM_HAS_LAYOUT_TABLE": true, "ESP_ROM_HAS_NEWLIB": true, "ESP_ROM_HAS_NEWLIB_NORMAL_FORMAT": true, "ESP_ROM_HAS_REGI2C_BUG": true, "ESP_ROM_HAS_RETARGETABLE_LOCKING": true, "ESP_ROM_HAS_RVFPLIB": true, "ESP_ROM_HAS_SPI_FLASH": true, "ESP_ROM_HAS_SW_FLOAT": true, "ESP_ROM_HAS_VERSION": true, "ESP_ROM_MULTI_HEAP_WALK_PATCH": true, "ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE": true, "ESP_ROM_RAM_APP_NEEDS_MMU_INIT": true, "ESP_ROM_REV0_HAS_NO_ECDSA_INTERFACE": true, "ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB": true, "ESP_ROM_TLSF_CHECK_PATCH": true, "ESP_ROM_UART_CLK_IS_XTAL": true, "ESP_ROM_USB_OTG_NUM": -1, "ESP_ROM_USB_SERIAL_DEVICE_NUM": 3, "ESP_ROM_WDT_INIT_PATCH": true, "ESP_SLEEP_CACHE_SAFE_ASSERTION": false, "ESP_SLEEP_DEBUG": false, "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND": true, "ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS": true, "ESP_SLEEP_GPIO_RESET_WORKAROUND": true, "ESP_SLEEP_MSPI_NEED_ALL_IO_PU": false, "ESP_SLEEP_POWER_DOWN_FLASH": false, "ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY": 0, "ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM": true, "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP": true, "ESP_SYSTEM_BBPLL_RECALIB": true, "ESP_SYSTEM_BROWNOUT_INTR": true, "ESP_SYSTEM_CHECK_INT_LEVEL_4": true, "ESP_SYSTEM_EVENT_QUEUE_SIZE": 32, "ESP_SYSTEM_EVENT_TASK_STACK_SIZE": 2304, "ESP_SYSTEM_GDBSTUB_RUNTIME": false, "ESP_SYSTEM_HW_PC_RECORD": true, "ESP_SYSTEM_HW_STACK_GUARD": true, "ESP_SYSTEM_PANIC_GDBSTUB": false, "ESP_SYSTEM_PANIC_PRINT_HALT": false, "ESP_SYSTEM_PANIC_PRINT_REBOOT": true, "ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS": 0, "ESP_SYSTEM_PANIC_SILENT_REBOOT": false, "ESP_SYSTEM_PMP_IDRAM_SPLIT": true, "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK": true, "ESP_SYSTEM_SINGLE_CORE_MODE": true, "ESP_SYSTEM_USE_EH_FRAME": false, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0": true, "ESP_TASK_WDT_EN": true, "ESP_TASK_WDT_INIT": true, "ESP_TASK_WDT_PANIC": false, "ESP_TASK_WDT_TIMEOUT_S": 5, "ESP_TIMER_IMPL_SYSTIMER": true, "ESP_TIMER_INTERRUPT_LEVEL": 1, "ESP_TIMER_ISR_AFFINITY_CPU0": true, "ESP_TIMER_PROFILING": false, "ESP_TIMER_SHOW_EXPERIMENTAL": false, "ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD": false, "ESP_TIMER_TASK_AFFINITY": 0, "ESP_TIMER_TASK_AFFINITY_CPU0": true, "ESP_TIMER_TASK_STACK_SIZE": 3584, "ESP_TIME_FUNCS_USE_ESP_TIMER": true, "ESP_TIME_FUNCS_USE_RTC_TIMER": true, "ESP_TLS_CLIENT_SESSION_TICKETS": false, "ESP_TLS_INSECURE": false, "ESP_TLS_PSK_VERIFICATION": false, "ESP_TLS_SERVER_CERT_SELECT_HOOK": false, "ESP_TLS_SERVER_MIN_AUTH_MODE_OPTIONAL": false, "ESP_TLS_SERVER_SESSION_TICKETS": false, "ESP_TLS_USE_DS_PERIPHERAL": true, "ESP_TLS_USING_MBEDTLS": true, "ESP_WIFI_11KV_SUPPORT": false, "ESP_WIFI_11R_SUPPORT": false, "ESP_WIFI_AMPDU_RX_ENABLED": true, "ESP_WIFI_AMPDU_TX_ENABLED": true, "ESP_WIFI_CSI_ENABLED": false, "ESP_WIFI_DEBUG_PRINT": false, "ESP_WIFI_DPP_SUPPORT": false, "ESP_WIFI_DYNAMIC_RX_BUFFER_NUM": 32, "ESP_WIFI_DYNAMIC_RX_MGMT_BUF": 0, "ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER": false, "ESP_WIFI_DYNAMIC_TX_BUFFER": true, "ESP_WIFI_DYNAMIC_TX_BUFFER_NUM": 32, "ESP_WIFI_ENABLED": true, "ESP_WIFI_ENABLE_SAE_PK": true, "ESP_WIFI_ENABLE_WIFI_RX_STATS": false, "ESP_WIFI_ENABLE_WIFI_TX_STATS": false, "ESP_WIFI_ENABLE_WPA3_OWE_STA": true, "ESP_WIFI_ENABLE_WPA3_SAE": true, "ESP_WIFI_ENTERPRISE_SUPPORT": true, "ESP_WIFI_ENT_FREE_DYNAMIC_BUFFER": false, "ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM": 7, "ESP_WIFI_EXTRA_IRAM_OPT": true, "ESP_WIFI_FTM_ENABLE": false, "ESP_WIFI_GCMP_SUPPORT": false, "ESP_WIFI_GMAC_SUPPORT": true, "ESP_WIFI_IRAM_OPT": true, "ESP_WIFI_MBEDTLS_CRYPTO": true, "ESP_WIFI_MBEDTLS_TLS_CLIENT": true, "ESP_WIFI_MBO_SUPPORT": false, "ESP_WIFI_MGMT_SBUF_NUM": 32, "ESP_WIFI_NVS_ENABLED": true, "ESP_WIFI_RX_BA_WIN": 6, "ESP_WIFI_RX_IRAM_OPT": true, "ESP_WIFI_RX_MGMT_BUF_NUM_DEF": 5, "ESP_WIFI_SLP_BEACON_LOST_OPT": false, "ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME": 10, "ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME": 50, "ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME": 15, "ESP_WIFI_SLP_IRAM_OPT": true, "ESP_WIFI_SOFTAP_BEACON_MAX_LEN": 752, "ESP_WIFI_SOFTAP_SAE_SUPPORT": true, "ESP_WIFI_SOFTAP_SUPPORT": true, "ESP_WIFI_STATIC_RX_BUFFER_NUM": 10, "ESP_WIFI_STATIC_RX_MGMT_BUFFER": true, "ESP_WIFI_STATIC_TX_BUFFER": false, "ESP_WIFI_STA_DISCONNECTED_PM_ENABLE": true, "ESP_WIFI_SUITE_B_192": false, "ESP_WIFI_TESTING_OPTIONS": false, "ESP_WIFI_TX_BA_WIN": 6, "ESP_WIFI_TX_BUFFER_TYPE": 1, "ESP_WIFI_TX_HETB_QUEUE_NUM": 3, "ESP_WIFI_WAPI_PSK": false, "ESP_WIFI_WPS_PASSPHRASE": false, "ESP_WIFI_WPS_SOFTAP_REGISTRAR": false, "ESP_WIFI_WPS_STRICT": false, "ETH_ENABLED": true, "ETH_SPI_ETHERNET_DM9051": false, "ETH_SPI_ETHERNET_KSZ8851SNL": false, "ETH_SPI_ETHERNET_W5500": false, "ETH_TRANSMIT_MUTEX": false, "ETH_USE_OPENETH": false, "ETH_USE_SPI_ETHERNET": true, "ETM_ENABLE_DEBUG_LOG": false, "FATFS_API_ENCODING_ANSI_OEM": true, "FATFS_API_ENCODING_UTF_8": false, "FATFS_CODEPAGE": 437, "FATFS_CODEPAGE_437": true, "FATFS_CODEPAGE_720": false, "FATFS_CODEPAGE_737": false, "FATFS_CODEPAGE_771": false, "FATFS_CODEPAGE_775": false, "FATFS_CODEPAGE_850": false, "FATFS_CODEPAGE_852": false, "FATFS_CODEPAGE_855": false, "FATFS_CODEPAGE_857": false, "FATFS_CODEPAGE_860": false, "FATFS_CODEPAGE_861": false, "FATFS_CODEPAGE_862": false, "FATFS_CODEPAGE_863": false, "FATFS_CODEPAGE_864": false, "FATFS_CODEPAGE_865": false, "FATFS_CODEPAGE_866": false, "FATFS_CODEPAGE_869": false, "FATFS_CODEPAGE_932": false, "FATFS_CODEPAGE_936": false, "FATFS_CODEPAGE_949": false, "FATFS_CODEPAGE_950": false, "FATFS_CODEPAGE_DYNAMIC": false, "FATFS_FS_LOCK": 0, "FATFS_IMMEDIATE_FSYNC": false, "FATFS_LFN_HEAP": true, "FATFS_LFN_NONE": false, "FATFS_LFN_STACK": false, "FATFS_LINK_LOCK": true, "FATFS_MAX_LFN": 255, "FATFS_PER_FILE_CACHE": true, "FATFS_SECTOR_4096": false, "FATFS_SECTOR_512": true, "FATFS_TIMEOUT_MS": 10000, "FATFS_USE_FASTSEEK": false, "FATFS_USE_LABEL": false, "FATFS_VFS_FSTAT_BLKSIZE": 0, "FATFS_VOLUME_COUNT": 2, "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER": true, "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE": false, "FREERTOS_CHECK_STACKOVERFLOW_CANARY": true, "FREERTOS_CHECK_STACKOVERFLOW_NONE": false, "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL": false, "FREERTOS_CORETIMER_SYSTIMER_LVL1": true, "FREERTOS_CORETIMER_SYSTIMER_LVL3": false, "FREERTOS_DEBUG_OCDAWARE": true, "FREERTOS_ENABLE_BACKWARD_COMPATIBILITY": false, "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP": false, "FREERTOS_ENABLE_TASK_SNAPSHOT": true, "FREERTOS_GENERATE_RUN_TIME_STATS": false, "FREERTOS_HZ": 1000, "FREERTOS_IDLE_TASK_STACKSIZE": 1536, "FREERTOS_INTERRUPT_BACKTRACE": true, "FREERTOS_ISR_STACKSIZE": 1536, "FREERTOS_MAX_TASK_NAME_LEN": 16, "FREERTOS_NO_AFFINITY": 2147483647, "FREERTOS_NUMBER_OF_CORES": 1, "FREERTOS_OPTIMIZED_SCHEDULER": true, "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH": false, "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH": true, "FREERTOS_PORT": true, "FREERTOS_QUEUE_REGISTRY_SIZE": 0, "FREERTOS_SMP": false, "FREERTOS_SUPPORT_STATIC_ALLOCATION": true, "FREERTOS_SYSTICK_USES_SYSTIMER": true, "FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES": 1, "FREERTOS_TASK_PRE_DELETION_HOOK": false, "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS": 1, "FREERTOS_TICK_SUPPORT_SYSTIMER": true, "FREERTOS_TIMER_QUEUE_LENGTH": 10, "FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY": 2147483647, "FREERTOS_TIMER_SERVICE_TASK_NAME": "Tmr Svc", "FREERTOS_TIMER_TASK_AFFINITY_CPU0": false, "FREERTOS_TIMER_TASK_NO_AFFINITY": true, "FREERTOS_TIMER_TASK_PRIORITY": 1, "FREERTOS_TIMER_TASK_STACK_DEPTH": 2048, "FREERTOS_TLSP_DELETION_CALLBACKS": true, "FREERTOS_UNICORE": true, "FREERTOS_USE_APPLICATION_TASK_TAG": false, "FREERTOS_USE_IDLE_HOOK": false, "FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES": false, "FREERTOS_USE_TICK_HOOK": false, "FREERTOS_USE_TRACE_FACILITY": false, "FREERTOS_WATCHPOINT_END_OF_STACK": false, "GDMA_CTRL_FUNC_IN_IRAM": true, "GDMA_ENABLE_DEBUG_LOG": false, "GDMA_ISR_IRAM_SAFE": false, "GPIO_CTRL_FUNC_IN_IRAM": false, "GPTIMER_CTRL_FUNC_IN_IRAM": false, "GPTIMER_ENABLE_DEBUG_LOG": false, "GPTIMER_ISR_HANDLER_IN_IRAM": true, "GPTIMER_ISR_IRAM_SAFE": false, "GPTIMER_SUPPRESS_DEPRECATE_WARN": false, "HAL_ASSERTION_DISABLE": false, "HAL_ASSERTION_ENABLE": false, "HAL_ASSERTION_EQUALS_SYSTEM": true, "HAL_ASSERTION_SILENT": false, "HAL_DEFAULT_ASSERTION_LEVEL": 2, "HAL_SPI_MASTER_FUNC_IN_IRAM": true, "HAL_SPI_SLAVE_FUNC_IN_IRAM": true, "HAL_SYSTIMER_USE_ROM_IMPL": true, "HAL_WDT_USE_ROM_IMPL": true, "HEAP_ABORT_WHEN_ALLOCATION_FAILS": false, "HEAP_POISONING_COMPREHENSIVE": false, "HEAP_POISONING_DISABLED": true, "HEAP_POISONING_LIGHT": false, "HEAP_TASK_TRACKING": false, "HEAP_TLSF_USE_ROM_IMPL": true, "HEAP_TRACING_OFF": true, "HEAP_TRACING_STANDALONE": false, "HEAP_TRACING_TOHOST": false, "HEAP_USE_HOOKS": false, "HTTPD_ERR_RESP_NO_DELAY": true, "HTTPD_LOG_PURGE_DATA": false, "HTTPD_MAX_REQ_HDR_LEN": 512, "HTTPD_MAX_URI_LEN": 512, "HTTPD_PURGE_BUF_LEN": 32, "HTTPD_QUEUE_WORK_BLOCKING": false, "HTTPD_WS_SUPPORT": false, "I2C_ENABLE_DEBUG_LOG": false, "I2C_ISR_IRAM_SAFE": false, "I2S_ENABLE_DEBUG_LOG": false, "I2S_ISR_IRAM_SAFE": false, "I2S_SUPPRESS_DEPRECATE_WARN": false, "IDF_CMAKE": true, "IDF_EXPERIMENTAL_FEATURES": false, "IDF_FIRMWARE_CHIP_ID": 13, "IDF_INIT_VERSION": "5.3.1", "IDF_TARGET": "esp32c6", "IDF_TARGET_ARCH": "riscv", "IDF_TARGET_ARCH_RISCV": true, "IDF_TARGET_ESP32C6": true, "IDF_TOOLCHAIN": "gcc", "IEEE802154_CCA_CARRIER": false, "IEEE802154_CCA_CARRIER_AND_ED": false, "IEEE802154_CCA_CARRIER_OR_ED": false, "IEEE802154_CCA_ED": true, "IEEE802154_CCA_MODE": 1, "IEEE802154_CCA_THRESHOLD": -60, "IEEE802154_DEBUG": false, "IEEE802154_ENABLED": true, "IEEE802154_MULTI_PAN_ENABLE": false, "IEEE802154_PENDING_TABLE_SIZE": 20, "IEEE802154_RX_BUFFER_SIZE": 20, "IEEE802154_TIMING_OPTIMIZATION": false, "LCD_ENABLE_DEBUG_LOG": false, "LEDC_CTRL_FUNC_IN_IRAM": false, "LOG_COLORS": false, "LOG_DEFAULT_LEVEL": 3, "LOG_DEFAULT_LEVEL_DEBUG": false, "LOG_DEFAULT_LEVEL_ERROR": false, "LOG_DEFAULT_LEVEL_INFO": true, "LOG_DEFAULT_LEVEL_NONE": false, "LOG_DEFAULT_LEVEL_VERBOSE": false, "LOG_DEFAULT_LEVEL_WARN": false, "LOG_MASTER_LEVEL": false, "LOG_MAXIMUM_EQUALS_DEFAULT": true, "LOG_MAXIMUM_LEVEL": 3, "LOG_MAXIMUM_LEVEL_DEBUG": false, "LOG_MAXIMUM_LEVEL_VERBOSE": false, "LOG_TIMESTAMP_SOURCE_RTOS": true, "LOG_TIMESTAMP_SOURCE_SYSTEM": false, "LV_ASSERT_HANDLER_INCLUDE": "assert.h", "LV_ATTRIBUTE_FAST_MEM_USE_IRAM": true, "LV_ATTRIBUTE_MEM_ALIGN_SIZE": 1, "LV_BIG_ENDIAN_SYSTEM": false, "LV_BUILD_EXAMPLES": true, "LV_CALENDAR_WEEK_STARTS_MONDAY": false, "LV_CIRCLE_CACHE_SIZE": 4, "LV_COLOR_16_SWAP": true, "LV_COLOR_CHROMA_KEY_HEX": 65280, "LV_COLOR_DEPTH": 16, "LV_COLOR_DEPTH_1": false, "LV_COLOR_DEPTH_16": true, "LV_COLOR_DEPTH_32": false, "LV_COLOR_DEPTH_8": false, "LV_COLOR_MIX_ROUND_OFS": 128, "LV_COLOR_SCREEN_TRANSP": true, "LV_CONF_MINIMAL": false, "LV_CONF_SKIP": true, "LV_DEMO_BENCHMARK_RGB565A8": false, "LV_DEMO_MUSIC_AUTO_PLAY": true, "LV_DEMO_MUSIC_LANDSCAPE": false, "LV_DEMO_MUSIC_LARGE": false, "LV_DEMO_MUSIC_ROUND": false, "LV_DEMO_MUSIC_SQUARE": false, "LV_DEMO_WIDGETS_SLIDESHOW": false, "LV_DISP_DEF_REFR_PERIOD": 30, "LV_DISP_ROT_MAX_BUF": 10240, "LV_DITHER_GRADIENT": false, "LV_DPI_DEF": 130, "LV_DRAW_COMPLEX": true, "LV_ENABLE_GC": false, "LV_FONT_CUSTOM": false, "LV_FONT_DEFAULT_DEJAVU_16_PERSIAN_HEBREW": false, "LV_FONT_DEFAULT_MONTSERRAT_12": false, "LV_FONT_DEFAULT_MONTSERRAT_12_SUBPX": false, "LV_FONT_DEFAULT_MONTSERRAT_14": true, "LV_FONT_DEFAULT_MONTSERRAT_16": false, "LV_FONT_DEFAULT_MONTSERRAT_18": false, "LV_FONT_DEFAULT_MONTSERRAT_20": false, "LV_FONT_DEFAULT_MONTSERRAT_22": false, "LV_FONT_DEFAULT_MONTSERRAT_24": false, "LV_FONT_DEFAULT_MONTSERRAT_26": false, "LV_FONT_DEFAULT_MONTSERRAT_28": false, "LV_FONT_DEFAULT_MONTSERRAT_28_COMPRESSED": false, "LV_FONT_DEFAULT_MONTSERRAT_30": false, "LV_FONT_DEFAULT_MONTSERRAT_32": false, "LV_FONT_DEFAULT_MONTSERRAT_34": false, "LV_FONT_DEFAULT_MONTSERRAT_36": false, "LV_FONT_DEFAULT_MONTSERRAT_38": false, "LV_FONT_DEFAULT_MONTSERRAT_40": false, "LV_FONT_DEFAULT_MONTSERRAT_42": false, "LV_FONT_DEFAULT_MONTSERRAT_44": false, "LV_FONT_DEFAULT_MONTSERRAT_46": false, "LV_FONT_DEFAULT_MONTSERRAT_48": false, "LV_FONT_DEFAULT_MONTSERRAT_8": false, "LV_FONT_DEFAULT_SIMSUN_16_CJK": false, "LV_FONT_DEFAULT_UNSCII_16": false, "LV_FONT_DEFAULT_UNSCII_8": false, "LV_FONT_DEJAVU_16_PERSIAN_HEBREW": false, "LV_FONT_FMT_TXT_LARGE": false, "LV_FONT_MONTSERRAT_10": false, "LV_FONT_MONTSERRAT_12": true, "LV_FONT_MONTSERRAT_12_SUBPX": false, "LV_FONT_MONTSERRAT_14": true, "LV_FONT_MONTSERRAT_16": true, "LV_FONT_MONTSERRAT_18": false, "LV_FONT_MONTSERRAT_20": false, "LV_FONT_MONTSERRAT_22": false, "LV_FONT_MONTSERRAT_24": false, "LV_FONT_MONTSERRAT_26": false, "LV_FONT_MONTSERRAT_28": false, "LV_FONT_MONTSERRAT_28_COMPRESSED": false, "LV_FONT_MONTSERRAT_30": false, "LV_FONT_MONTSERRAT_32": false, "LV_FONT_MONTSERRAT_34": false, "LV_FONT_MONTSERRAT_36": false, "LV_FONT_MONTSERRAT_38": false, "LV_FONT_MONTSERRAT_40": false, "LV_FONT_MONTSERRAT_42": false, "LV_FONT_MONTSERRAT_44": false, "LV_FONT_MONTSERRAT_46": false, "LV_FONT_MONTSERRAT_48": false, "LV_FONT_MONTSERRAT_8": false, "LV_FONT_SIMSUN_16_CJK": false, "LV_FONT_UNSCII_16": false, "LV_FONT_UNSCII_8": false, "LV_GRADIENT_MAX_STOPS": 2, "LV_GRAD_CACHE_DEF_SIZE": 0, "LV_IMG_CACHE_DEF_SIZE": 0, "LV_INDEV_DEF_READ_PERIOD": 30, "LV_LABEL_LONG_TXT_HINT": true, "LV_LABEL_TEXT_SELECTION": true, "LV_LAYER_SIMPLE_BUF_SIZE": 24576, "LV_MEMCPY_MEMSET_STD": true, "LV_MEM_BUF_MAX_NUM": 16, "LV_MEM_CUSTOM": true, "LV_MEM_CUSTOM_INCLUDE": "stdlib.h", "LV_PERF_MONITOR_ALIGN_BOTTOM_LEFT": false, "LV_PERF_MONITOR_ALIGN_BOTTOM_MID": false, "LV_PERF_MONITOR_ALIGN_BOTTOM_RIGHT": true, "LV_PERF_MONITOR_ALIGN_CENTER": false, "LV_PERF_MONITOR_ALIGN_LEFT_MID": false, "LV_PERF_MONITOR_ALIGN_RIGHT_MID": false, "LV_PERF_MONITOR_ALIGN_TOP_LEFT": false, "LV_PERF_MONITOR_ALIGN_TOP_MID": false, "LV_PERF_MONITOR_ALIGN_TOP_RIGHT": false, "LV_ROLLER_INF_PAGES": 7, "LV_SHADOW_CACHE_SIZE": 0, "LV_SPAN_SNIPPET_STACK_SIZE": 64, "LV_SPRINTF_CUSTOM": false, "LV_SPRINTF_USE_FLOAT": false, "LV_TEXTAREA_DEF_PWD_SHOW_TIME": 1500, "LV_THEME_DEFAULT_DARK": false, "LV_THEME_DEFAULT_GROW": true, "LV_THEME_DEFAULT_TRANSITION_TIME": 80, "LV_TICK_CUSTOM": false, "LV_TXT_BREAK_CHARS": " ,.;:-_", "LV_TXT_COLOR_CMD": "#", "LV_TXT_ENC_ASCII": false, "LV_TXT_ENC_UTF8": true, "LV_TXT_LINE_BREAK_LONG_LEN": 0, "LV_USE_ANIMIMG": true, "LV_USE_ARABIC_PERSIAN_CHARS": false, "LV_USE_ARC": true, "LV_USE_ASSERT_MALLOC": true, "LV_USE_ASSERT_MEM_INTEGRITY": false, "LV_USE_ASSERT_NULL": true, "LV_USE_ASSERT_OBJ": false, "LV_USE_ASSERT_STYLE": false, "LV_USE_BAR": true, "LV_USE_BIDI": false, "LV_USE_BMP": false, "LV_USE_BTN": true, "LV_USE_BTNMATRIX": true, "LV_USE_CALENDAR": true, "LV_USE_CALENDAR_HEADER_ARROW": true, "LV_USE_CALENDAR_HEADER_DROPDOWN": true, "LV_USE_CANVAS": true, "LV_USE_CHART": true, "LV_USE_CHECKBOX": true, "LV_USE_COLORWHEEL": true, "LV_USE_DEMO_BENCHMARK": true, "LV_USE_DEMO_KEYPAD_AND_ENCODER": false, "LV_USE_DEMO_MUSIC": true, "LV_USE_DEMO_STRESS": true, "LV_USE_DEMO_WIDGETS": true, "LV_USE_DROPDOWN": true, "LV_USE_FFMPEG": false, "LV_USE_FLEX": true, "LV_USE_FONT_COMPRESSED": false, "LV_USE_FONT_PLACEHOLDER": true, "LV_USE_FONT_SUBPX": false, "LV_USE_FRAGMENT": false, "LV_USE_FREETYPE": false, "LV_USE_FS_FATFS": false, "LV_USE_FS_LITTLEFS": false, "LV_USE_FS_POSIX": false, "LV_USE_FS_STDIO": false, "LV_USE_FS_WIN32": false, "LV_USE_GIF": true, "LV_USE_GPU_ARM2D": false, "LV_USE_GPU_NXP_PXP": false, "LV_USE_GPU_NXP_VG_LITE": false, "LV_USE_GPU_RA6M3_G2D": false, "LV_USE_GPU_SDL": false, "LV_USE_GPU_STM32_DMA2D": false, "LV_USE_GPU_SWM341_DMA2D": false, "LV_USE_GRID": true, "LV_USE_GRIDNAV": false, "LV_USE_IME_PINYIN": false, "LV_USE_IMG": true, "LV_USE_IMGBTN": true, "LV_USE_IMGFONT": false, "LV_USE_KEYBOARD": true, "LV_USE_LABEL": true, "LV_USE_LARGE_COORD": false, "LV_USE_LED": true, "LV_USE_LINE": true, "LV_USE_LIST": true, "LV_USE_LOG": false, "LV_USE_MENU": true, "LV_USE_METER": true, "LV_USE_MONKEY": false, "LV_USE_MSG": false, "LV_USE_MSGBOX": true, "LV_USE_PERF_MONITOR": true, "LV_USE_PNG": false, "LV_USE_QRCODE": false, "LV_USE_REFR_DEBUG": false, "LV_USE_RLOTTIE": false, "LV_USE_ROLLER": true, "LV_USE_SJPG": false, "LV_USE_SLIDER": true, "LV_USE_SNAPSHOT": true, "LV_USE_SPAN": true, "LV_USE_SPINBOX": true, "LV_USE_SPINNER": true, "LV_USE_SWITCH": true, "LV_USE_TABLE": true, "LV_USE_TABVIEW": true, "LV_USE_TEXTAREA": true, "LV_USE_THEME_BASIC": true, "LV_USE_THEME_DEFAULT": true, "LV_USE_THEME_MONO": false, "LV_USE_TILEVIEW": true, "LV_USE_TINY_TTF": false, "LV_USE_USER_DATA": true, "LV_USE_WIN": true, "LWIP_AUTOIP": false, "LWIP_BRIDGEIF_MAX_PORTS": 7, "LWIP_BROADCAST_PING": false, "LWIP_CHECKSUM_CHECK_ICMP": true, "LWIP_CHECKSUM_CHECK_IP": false, "LWIP_CHECKSUM_CHECK_UDP": false, "LWIP_CHECK_THREAD_SAFETY": false, "LWIP_DEBUG": false, "LWIP_DHCPS": true, "LWIP_DHCPS_LEASE_UNIT": 60, "LWIP_DHCPS_MAX_STATION_NUM": 8, "LWIP_DHCPS_STATIC_ENTRIES": true, "LWIP_DHCP_COARSE_TIMER_SECS": 1, "LWIP_DHCP_DISABLE_CLIENT_ID": false, "LWIP_DHCP_DISABLE_VENDOR_CLASS_ID": true, "LWIP_DHCP_DOES_ARP_CHECK": true, "LWIP_DHCP_GET_NTP_SRV": false, "LWIP_DHCP_OPTIONS_LEN": 68, "LWIP_DHCP_RESTORE_LAST_IP": false, "LWIP_DNS_MAX_SERVERS": 3, "LWIP_DNS_SUPPORT_MDNS_QUERIES": true, "LWIP_ENABLE": true, "LWIP_ESP_GRATUITOUS_ARP": true, "LWIP_ESP_LWIP_ASSERT": true, "LWIP_ESP_MLDV6_REPORT": true, "LWIP_EXTRA_IRAM_OPTIMIZATION": false, "LWIP_FALLBACK_DNS_SERVER_SUPPORT": false, "LWIP_FORCE_ROUTER_FORWARDING": false, "LWIP_GARP_TMR_INTERVAL": 60, "LWIP_HOOK_IP6_INPUT_CUSTOM": false, "LWIP_HOOK_IP6_INPUT_DEFAULT": true, "LWIP_HOOK_IP6_INPUT_NONE": false, "LWIP_HOOK_IP6_ROUTE_CUSTOM": false, "LWIP_HOOK_IP6_ROUTE_DEFAULT": false, "LWIP_HOOK_IP6_ROUTE_NONE": true, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM": false, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT": false, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE": true, "LWIP_HOOK_ND6_GET_GW_CUSTOM": false, "LWIP_HOOK_ND6_GET_GW_DEFAULT": false, "LWIP_HOOK_ND6_GET_GW_NONE": true, "LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM": false, "LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT": false, "LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE": true, "LWIP_HOOK_TCP_ISN_CUSTOM": false, "LWIP_HOOK_TCP_ISN_DEFAULT": true, "LWIP_HOOK_TCP_ISN_NONE": false, "LWIP_ICMP": true, "LWIP_IP4_FRAG": true, "LWIP_IP4_REASSEMBLY": false, "LWIP_IP6_FRAG": true, "LWIP_IP6_REASSEMBLY": false, "LWIP_IPV4": true, "LWIP_IPV6": true, "LWIP_IPV6_AUTOCONFIG": false, "LWIP_IPV6_FORWARD": false, "LWIP_IPV6_MEMP_NUM_ND6_QUEUE": 3, "LWIP_IPV6_ND6_NUM_NEIGHBORS": 5, "LWIP_IPV6_NUM_ADDRESSES": 3, "LWIP_IP_DEFAULT_TTL": 64, "LWIP_IP_FORWARD": false, "LWIP_IP_REASS_MAX_PBUFS": 10, "LWIP_IRAM_OPTIMIZATION": false, "LWIP_L2_TO_L3_COPY": false, "LWIP_LOCAL_HOSTNAME": "espressif", "LWIP_LOOPBACK_MAX_PBUFS": 8, "LWIP_MAX_ACTIVE_TCP": 16, "LWIP_MAX_LISTENING_TCP": 16, "LWIP_MAX_RAW_PCBS": 16, "LWIP_MAX_SOCKETS": 10, "LWIP_MAX_UDP_PCBS": 16, "LWIP_MLDV6_TMR_INTERVAL": 40, "LWIP_MULTICAST_PING": false, "LWIP_ND6": true, "LWIP_NETBUF_RECVINFO": false, "LWIP_NETIF_API": false, "LWIP_NETIF_LOOPBACK": true, "LWIP_NETIF_STATUS_CALLBACK": false, "LWIP_NUM_NETIF_CLIENT_DATA": 0, "LWIP_PPP_SUPPORT": false, "LWIP_SLIP_SUPPORT": false, "LWIP_SNTP_MAXIMUM_STARTUP_DELAY": 5000, "LWIP_SNTP_MAX_SERVERS": 1, "LWIP_SNTP_STARTUP_DELAY": true, "LWIP_SNTP_UPDATE_DELAY": 3600000, "LWIP_SO_LINGER": false, "LWIP_SO_RCVBUF": false, "LWIP_SO_REUSE": true, "LWIP_SO_REUSE_RXTOALL": true, "LWIP_STATS": false, "LWIP_TCPIP_CORE_LOCKING": false, "LWIP_TCPIP_RECVMBOX_SIZE": 32, "LWIP_TCPIP_TASK_AFFINITY": 2147483647, "LWIP_TCPIP_TASK_AFFINITY_CPU0": false, "LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY": true, "LWIP_TCPIP_TASK_PRIO": 18, "LWIP_TCPIP_TASK_STACK_SIZE": 3072, "LWIP_TCP_ACCEPTMBOX_SIZE": 6, "LWIP_TCP_FIN_WAIT_TIMEOUT": 20000, "LWIP_TCP_HIGH_SPEED_RETRANSMISSION": true, "LWIP_TCP_MAXRTX": 12, "LWIP_TCP_MSL": 60000, "LWIP_TCP_MSS": 1440, "LWIP_TCP_OOSEQ_MAX_PBUFS": 4, "LWIP_TCP_OOSEQ_TIMEOUT": 6, "LWIP_TCP_OVERSIZE_DISABLE": false, "LWIP_TCP_OVERSIZE_MSS": true, "LWIP_TCP_OVERSIZE_QUARTER_MSS": false, "LWIP_TCP_QUEUE_OOSEQ": true, "LWIP_TCP_RECVMBOX_SIZE": 6, "LWIP_TCP_RTO_TIME": 1500, "LWIP_TCP_SACK_OUT": false, "LWIP_TCP_SND_BUF_DEFAULT": 5760, "LWIP_TCP_SYNMAXRTX": 12, "LWIP_TCP_TMR_INTERVAL": 250, "LWIP_TCP_WND_DEFAULT": 5760, "LWIP_TIMERS_ONDEMAND": true, "LWIP_UDP_RECVMBOX_SIZE": 6, "LWIP_USE_ONLY_LWIP_SELECT": false, "MBEDTLS_AES_C": true, "MBEDTLS_AES_INTERRUPT_LEVEL": 0, "MBEDTLS_AES_USE_INTERRUPT": true, "MBEDTLS_ASYMMETRIC_CONTENT_LEN": true, "MBEDTLS_ATCA_HW_ECDSA_SIGN": false, "MBEDTLS_ATCA_HW_ECDSA_VERIFY": false, "MBEDTLS_BLOWFISH_C": false, "MBEDTLS_CAMELLIA_C": false, "MBEDTLS_CCM_C": true, "MBEDTLS_CERTIFICATE_BUNDLE": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN": false, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE": false, "MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST": false, "MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS": 200, "MBEDTLS_CHACHA20_C": false, "MBEDTLS_CLIENT_SSL_SESSION_TICKETS": true, "MBEDTLS_CMAC_C": true, "MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE": false, "MBEDTLS_CUSTOM_MEM_ALLOC": false, "MBEDTLS_DEBUG": false, "MBEDTLS_DEFAULT_MEM_ALLOC": false, "MBEDTLS_DES_C": false, "MBEDTLS_DHM_C": false, "MBEDTLS_DYNAMIC_BUFFER": false, "MBEDTLS_ECC_OTHER_CURVES_SOFT_FALLBACK": true, "MBEDTLS_ECDH_C": true, "MBEDTLS_ECDSA_C": true, "MBEDTLS_ECDSA_DETERMINISTIC": true, "MBEDTLS_ECJPAKE_C": false, "MBEDTLS_ECP_C": true, "MBEDTLS_ECP_DP_BP256R1_ENABLED": true, "MBEDTLS_ECP_DP_BP384R1_ENABLED": true, "MBEDTLS_ECP_DP_BP512R1_ENABLED": true, "MBEDTLS_ECP_DP_CURVE25519_ENABLED": true, "MBEDTLS_ECP_DP_SECP192K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP192R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP224K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP224R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP256K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP256R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP384R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP521R1_ENABLED": true, "MBEDTLS_ECP_FIXED_POINT_OPTIM": false, "MBEDTLS_ECP_NIST_OPTIM": true, "MBEDTLS_ECP_RESTARTABLE": false, "MBEDTLS_ERROR_STRINGS": true, "MBEDTLS_GCM_C": true, "MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER": true, "MBEDTLS_HARDWARE_AES": true, "MBEDTLS_HARDWARE_ECC": true, "MBEDTLS_HARDWARE_MPI": true, "MBEDTLS_HARDWARE_SHA": true, "MBEDTLS_HAVE_TIME": true, "MBEDTLS_HAVE_TIME_DATE": false, "MBEDTLS_HKDF_C": false, "MBEDTLS_INTERNAL_MEM_ALLOC": true, "MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA": true, "MBEDTLS_KEY_EXCHANGE_ECDHE_RSA": true, "MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA": true, "MBEDTLS_KEY_EXCHANGE_ECDH_RSA": true, "MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE": true, "MBEDTLS_KEY_EXCHANGE_RSA": true, "MBEDTLS_LARGE_KEY_SOFTWARE_MPI": true, "MBEDTLS_MPI_INTERRUPT_LEVEL": 0, "MBEDTLS_MPI_USE_INTERRUPT": true, "MBEDTLS_NIST_KW_C": false, "MBEDTLS_PEM_PARSE_C": true, "MBEDTLS_PEM_WRITE_C": true, "MBEDTLS_PKCS7_C": true, "MBEDTLS_PLATFORM_TIME_ALT": false, "MBEDTLS_POLY1305_C": false, "MBEDTLS_PSK_MODES": false, "MBEDTLS_RIPEMD160_C": false, "MBEDTLS_ROM_MD5": true, "MBEDTLS_SERVER_SSL_SESSION_TICKETS": true, "MBEDTLS_SHA512_C": true, "MBEDTLS_SSL_ALPN": true, "MBEDTLS_SSL_CONTEXT_SERIALIZATION": false, "MBEDTLS_SSL_IN_CONTENT_LEN": 16384, "MBEDTLS_SSL_KEEP_PEER_CERTIFICATE": true, "MBEDTLS_SSL_OUT_CONTENT_LEN": 4096, "MBEDTLS_SSL_PROTO_DTLS": false, "MBEDTLS_SSL_PROTO_GMTSSL1_1": false, "MBEDTLS_SSL_PROTO_TLS1_2": true, "MBEDTLS_SSL_PROTO_TLS1_3": false, "MBEDTLS_SSL_RENEGOTIATION": true, "MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH": false, "MBEDTLS_THREADING_C": false, "MBEDTLS_TLS_CLIENT": true, "MBEDTLS_TLS_CLIENT_ONLY": false, "MBEDTLS_TLS_DISABLED": false, "MBEDTLS_TLS_ENABLED": true, "MBEDTLS_TLS_SERVER": true, "MBEDTLS_TLS_SERVER_AND_CLIENT": true, "MBEDTLS_TLS_SERVER_ONLY": false, "MBEDTLS_X509_CRL_PARSE_C": true, "MBEDTLS_X509_CSR_PARSE_C": true, "MBEDTLS_X509_TRUSTED_CERT_CALLBACK": false, "MBEDTLS_XTEA_C": false, "MCPWM_CTRL_FUNC_IN_IRAM": false, "MCPWM_ENABLE_DEBUG_LOG": false, "MCPWM_ISR_IRAM_SAFE": false, "MCPWM_SUPPRESS_DEPRECATE_WARN": false, "MMAP_FILE_NAME_LENGTH": 16, "MMU_PAGE_MODE": "64KB", "MMU_PAGE_SIZE": 65536, "MMU_PAGE_SIZE_64KB": true, "MQTT_CUSTOM_OUTBOX": false, "MQTT_MSG_ID_INCREMENTAL": false, "MQTT_PROTOCOL_311": true, "MQTT_PROTOCOL_5": false, "MQTT_REPORT_DELETED_MESSAGES": false, "MQTT_SKIP_PUBLISH_IF_DISCONNECTED": false, "MQTT_TASK_CORE_SELECTION_ENABLED": false, "MQTT_TRANSPORT_SSL": true, "MQTT_TRANSPORT_WEBSOCKET": true, "MQTT_TRANSPORT_WEBSOCKET_SECURE": true, "MQTT_USE_CUSTOM_CONFIG": false, "NEWLIB_NANO_FORMAT": false, "NEWLIB_STDIN_LINE_ENDING_CR": true, "NEWLIB_STDIN_LINE_ENDING_CRLF": false, "NEWLIB_STDIN_LINE_ENDING_LF": false, "NEWLIB_STDOUT_LINE_ENDING_CR": false, "NEWLIB_STDOUT_LINE_ENDING_CRLF": true, "NEWLIB_STDOUT_LINE_ENDING_LF": false, "NEWLIB_TIME_SYSCALL_USE_HRT": false, "NEWLIB_TIME_SYSCALL_USE_NONE": false, "NEWLIB_TIME_SYSCALL_USE_RTC": false, "NEWLIB_TIME_SYSCALL_USE_RTC_HRT": true, "NVS_ASSERT_ERROR_CHECK": false, "NVS_ENCRYPTION": false, "NVS_LEGACY_DUP_KEYS_COMPATIBILITY": false, "OPENTHREAD_ENABLED": false, "OPENTHREAD_MESH_LOCAL_PREFIX": "fd00:db8:a0:0::/64", "OPENTHREAD_NETWORK_CHANNEL": 15, "OPENTHREAD_NETWORK_EXTPANID": "dead00beef00cafe", "OPENTHREAD_NETWORK_MASTERKEY": "00112233445566778899aa<PERSON><PERSON><PERSON><PERSON><PERSON>", "OPENTHREAD_NETWORK_NAME": "OpenThread-ESP", "OPENTHREAD_NETWORK_PANID": 4660, "OPENTHREAD_NETWORK_PSKC": "104810e2315100afd6bc9215a6bfac53", "OPENTHREAD_RX_ON_WHEN_IDLE": false, "OPENTHREAD_SPINEL_ONLY": false, "OPENTHREAD_XTAL_ACCURACY": 130, "PARLIO_ENABLE_DEBUG_LOG": false, "PARLIO_ISR_IRAM_SAFE": false, "PARTITION_TABLE_CUSTOM": true, "PARTITION_TABLE_CUSTOM_FILENAME": "partitions.csv", "PARTITION_TABLE_FILENAME": "partitions.csv", "PARTITION_TABLE_MD5": true, "PARTITION_TABLE_OFFSET": 32768, "PARTITION_TABLE_SINGLE_APP": false, "PARTITION_TABLE_SINGLE_APP_LARGE": false, "PARTITION_TABLE_TWO_OTA": false, "PCNT_CTRL_FUNC_IN_IRAM": false, "PCNT_ENABLE_DEBUG_LOG": false, "PCNT_ISR_IRAM_SAFE": false, "PCNT_SUPPRESS_DEPRECATE_WARN": false, "PERIPH_CTRL_FUNC_IN_IRAM": true, "PM_ENABLE": false, "PM_POWER_DOWN_CPU_IN_LIGHT_SLEEP": true, "PM_POWER_DOWN_PERIPHERAL_IN_LIGHT_SLEEP": false, "PM_SLP_DEFAULT_PARAMS_OPT": true, "PTHREAD_STACK_MIN": 768, "PTHREAD_TASK_CORE_DEFAULT": -1, "PTHREAD_TASK_NAME_DEFAULT": "pthread", "PTHREAD_TASK_PRIO_DEFAULT": 5, "PTHREAD_TASK_STACK_SIZE_DEFAULT": 3072, "RINGBUF_PLACE_FUNCTIONS_INTO_FLASH": false, "RMT_ENABLE_DEBUG_LOG": false, "RMT_ISR_IRAM_SAFE": false, "RMT_RECV_FUNC_IN_IRAM": false, "RMT_SUPPRESS_DEPRECATE_WARN": false, "RTC_CLK_CAL_CYCLES": 1024, "RTC_CLK_SRC_EXT_CRYS": false, "RTC_CLK_SRC_EXT_OSC": false, "RTC_CLK_SRC_INT_RC": true, "RTC_CLK_SRC_INT_RC32K": false, "SDM_CTRL_FUNC_IN_IRAM": false, "SDM_ENABLE_DEBUG_LOG": false, "SDM_SUPPRESS_DEPRECATE_WARN": false, "SECURE_BOOT": false, "SECURE_BOOT_V2_ECC_SUPPORTED": true, "SECURE_BOOT_V2_PREFERRED": true, "SECURE_BOOT_V2_RSA_SUPPORTED": true, "SECURE_FLASH_ENC_ENABLED": false, "SECURE_ROM_DL_MODE_ENABLED": true, "SECURE_SIGNED_APPS_NO_SECURE_BOOT": false, "SOC_ADC_ATTEN_NUM": 4, "SOC_ADC_CALIBRATION_V1_SUPPORTED": true, "SOC_ADC_CALIB_CHAN_COMPENS_SUPPORTED": true, "SOC_ADC_DIGI_CONTROLLER_NUM": 1, "SOC_ADC_DIGI_DATA_BYTES_PER_CONV": 4, "SOC_ADC_DIGI_IIR_FILTER_NUM": 2, "SOC_ADC_DIGI_MAX_BITWIDTH": 12, "SOC_ADC_DIGI_MIN_BITWIDTH": 12, "SOC_ADC_DIGI_MONITOR_NUM": 2, "SOC_ADC_DIGI_RESULT_BYTES": 4, "SOC_ADC_DIG_CTRL_SUPPORTED": true, "SOC_ADC_DIG_IIR_FILTER_SUPPORTED": true, "SOC_ADC_DMA_SUPPORTED": true, "SOC_ADC_MAX_CHANNEL_NUM": 7, "SOC_ADC_MONITOR_SUPPORTED": true, "SOC_ADC_PATT_LEN_MAX": 8, "SOC_ADC_PERIPH_NUM": 1, "SOC_ADC_RTC_MAX_BITWIDTH": 12, "SOC_ADC_RTC_MIN_BITWIDTH": 12, "SOC_ADC_SAMPLE_FREQ_THRES_HIGH": 83333, "SOC_ADC_SAMPLE_FREQ_THRES_LOW": 611, "SOC_ADC_SELF_HW_CALI_SUPPORTED": true, "SOC_ADC_SHARED_POWER": true, "SOC_ADC_SUPPORTED": true, "SOC_ADC_TEMPERATURE_SHARE_INTR": true, "SOC_AES_GDMA": true, "SOC_AES_SUPPORTED": true, "SOC_AES_SUPPORT_AES_128": true, "SOC_AES_SUPPORT_AES_256": true, "SOC_AES_SUPPORT_DMA": true, "SOC_AHB_GDMA_SUPPORTED": true, "SOC_AHB_GDMA_VERSION": 1, "SOC_APM_CTRL_FILTER_SUPPORTED": true, "SOC_APM_SUPPORTED": true, "SOC_ASSIST_DEBUG_SUPPORTED": true, "SOC_ASYNC_MEMCPY_SUPPORTED": true, "SOC_BLE_50_SUPPORTED": true, "SOC_BLE_DEVICE_PRIVACY_SUPPORTED": true, "SOC_BLE_MESH_SUPPORTED": true, "SOC_BLE_MULTI_CONN_OPTIMIZATION": true, "SOC_BLE_PERIODIC_ADV_ENH_SUPPORTED": true, "SOC_BLE_POWER_CONTROL_SUPPORTED": true, "SOC_BLE_SUPPORTED": true, "SOC_BLE_USE_WIFI_PWR_CLK_WORKAROUND": true, "SOC_BLUFI_SUPPORTED": true, "SOC_BOD_SUPPORTED": true, "SOC_BROWNOUT_RESET_SUPPORTED": true, "SOC_BT_SUPPORTED": true, "SOC_CACHE_FREEZE_SUPPORTED": true, "SOC_CAPS_NO_RESET_BY_ANA_BOD": true, "SOC_CLK_OSC_SLOW_SUPPORTED": true, "SOC_CLK_RC32K_SUPPORTED": true, "SOC_CLK_RC_FAST_SUPPORT_CALIBRATION": true, "SOC_CLK_TREE_SUPPORTED": true, "SOC_CLK_XTAL32K_SUPPORTED": true, "SOC_CLOCKOUT_HAS_SOURCE_GATE": true, "SOC_COEX_HW_PTI": true, "SOC_CPU_BREAKPOINTS_NUM": 4, "SOC_CPU_CORES_NUM": 1, "SOC_CPU_HAS_CSR_PC": true, "SOC_CPU_HAS_FLEXIBLE_INTC": true, "SOC_CPU_HAS_PMA": true, "SOC_CPU_IDRAM_SPLIT_USING_PMP": true, "SOC_CPU_INTR_NUM": 32, "SOC_CPU_WATCHPOINTS_NUM": 4, "SOC_CPU_WATCHPOINT_MAX_REGION_SIZE": 2147483648, "SOC_CRYPTO_DPA_PROTECTION_SUPPORTED": true, "SOC_DEDICATED_GPIO_SUPPORTED": true, "SOC_DEDIC_GPIO_IN_CHANNELS_NUM": 8, "SOC_DEDIC_GPIO_OUT_CHANNELS_NUM": 8, "SOC_DEDIC_PERIPH_ALWAYS_ENABLE": true, "SOC_DEEP_SLEEP_SUPPORTED": true, "SOC_DIG_SIGN_SUPPORTED": true, "SOC_DS_KEY_CHECK_MAX_WAIT_US": 1100, "SOC_DS_KEY_PARAM_MD_IV_LENGTH": 16, "SOC_DS_SIGNATURE_MAX_BIT_LEN": 3072, "SOC_ECC_SUPPORTED": true, "SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK": true, "SOC_EFUSE_DIS_DIRECT_BOOT": true, "SOC_EFUSE_DIS_DOWNLOAD_ICACHE": true, "SOC_EFUSE_DIS_ICACHE": true, "SOC_EFUSE_DIS_PAD_JTAG": true, "SOC_EFUSE_DIS_USB_JTAG": true, "SOC_EFUSE_KEY_PURPOSE_FIELD": true, "SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS": true, "SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS": 3, "SOC_EFUSE_SOFT_DIS_JTAG": true, "SOC_EFUSE_SUPPORTED": true, "SOC_ESP_NIMBLE_CONTROLLER": true, "SOC_ETM_CHANNELS_PER_GROUP": 50, "SOC_ETM_GROUPS": 1, "SOC_ETM_SUPPORTED": true, "SOC_EXTERNAL_COEX_ADVANCE": true, "SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX": 64, "SOC_FLASH_ENCRYPTION_XTS_AES": true, "SOC_FLASH_ENCRYPTION_XTS_AES_128": true, "SOC_FLASH_ENC_SUPPORTED": true, "SOC_GDMA_NUM_GROUPS_MAX": 1, "SOC_GDMA_PAIRS_PER_GROUP_MAX": 3, "SOC_GDMA_SUPPORTED": true, "SOC_GDMA_SUPPORT_ETM": true, "SOC_GDMA_SUPPORT_SLEEP_RETENTION": true, "SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX": true, "SOC_GPIO_CLOCKOUT_CHANNEL_NUM": 3, "SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT": 8, "SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK": 0, "SOC_GPIO_FLEX_GLITCH_FILTER_NUM": 8, "SOC_GPIO_IN_RANGE_MAX": 30, "SOC_GPIO_OUT_RANGE_MAX": 30, "SOC_GPIO_PIN_COUNT": 31, "SOC_GPIO_PORT": 1, "SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP": true, "SOC_GPIO_SUPPORT_ETM": true, "SOC_GPIO_SUPPORT_FORCE_HOLD": true, "SOC_GPIO_SUPPORT_HOLD_SINGLE_IO_IN_DSLP": true, "SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER": true, "SOC_GPIO_SUPPORT_RTC_INDEPENDENT": true, "SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK": 2147483392, "SOC_GPSPI_SUPPORTED": true, "SOC_GPTIMER_SUPPORTED": true, "SOC_HMAC_SUPPORTED": true, "SOC_HP_I2C_NUM": 1, "SOC_I2C_CMD_REG_NUM": 8, "SOC_I2C_FIFO_LEN": 32, "SOC_I2C_NUM": 2, "SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE": true, "SOC_I2C_SLAVE_SUPPORT_BROADCAST": true, "SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS": true, "SOC_I2C_SLAVE_SUPPORT_SLAVE_UNMATCH": true, "SOC_I2C_SUPPORTED": true, "SOC_I2C_SUPPORT_10BIT_ADDR": true, "SOC_I2C_SUPPORT_HW_CLR_BUS": true, "SOC_I2C_SUPPORT_HW_FSM_RST": true, "SOC_I2C_SUPPORT_RTC": true, "SOC_I2C_SUPPORT_SLAVE": true, "SOC_I2C_SUPPORT_SLEEP_RETENTION": true, "SOC_I2C_SUPPORT_XTAL": true, "SOC_I2S_HW_VERSION_2": true, "SOC_I2S_NUM": 1, "SOC_I2S_PDM_MAX_TX_LINES": 2, "SOC_I2S_SUPPORTED": true, "SOC_I2S_SUPPORTS_PCM": true, "SOC_I2S_SUPPORTS_PDM": true, "SOC_I2S_SUPPORTS_PDM_TX": true, "SOC_I2S_SUPPORTS_PLL_F160M": true, "SOC_I2S_SUPPORTS_TDM": true, "SOC_I2S_SUPPORTS_XTAL": true, "SOC_IEEE802154_SUPPORTED": true, "SOC_INT_PLIC_SUPPORTED": true, "SOC_LEDC_CHANNEL_NUM": 6, "SOC_LEDC_FADE_PARAMS_BIT_WIDTH": 10, "SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX": 16, "SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED": true, "SOC_LEDC_SUPPORTED": true, "SOC_LEDC_SUPPORT_FADE_STOP": true, "SOC_LEDC_SUPPORT_PLL_DIV_CLOCK": true, "SOC_LEDC_SUPPORT_XTAL_CLOCK": true, "SOC_LEDC_TIMER_BIT_WIDTH": 20, "SOC_LIGHT_SLEEP_SUPPORTED": true, "SOC_LP_AON_SUPPORTED": true, "SOC_LP_CORE_SINGLE_INTERRUPT_VECTOR": true, "SOC_LP_CORE_SUPPORTED": true, "SOC_LP_CORE_SUPPORT_ETM": true, "SOC_LP_I2C_FIFO_LEN": 16, "SOC_LP_I2C_NUM": 1, "SOC_LP_I2C_SUPPORTED": true, "SOC_LP_IO_CLOCK_IS_INDEPENDENT": true, "SOC_LP_PERIPHERALS_SUPPORTED": true, "SOC_LP_TIMER_BIT_WIDTH_HI": 16, "SOC_LP_TIMER_BIT_WIDTH_LO": 32, "SOC_LP_TIMER_SUPPORTED": true, "SOC_LP_UART_FIFO_LEN": 16, "SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER": 3, "SOC_MCPWM_CAPTURE_CLK_FROM_GROUP": true, "SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP": true, "SOC_MCPWM_COMPARATORS_PER_OPERATOR": 2, "SOC_MCPWM_GENERATORS_PER_OPERATOR": 2, "SOC_MCPWM_GPIO_FAULTS_PER_GROUP": 3, "SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP": 3, "SOC_MCPWM_GROUPS": 1, "SOC_MCPWM_OPERATORS_PER_GROUP": 3, "SOC_MCPWM_SUPPORTED": true, "SOC_MCPWM_SUPPORT_ETM": true, "SOC_MCPWM_SWSYNC_CAN_PROPAGATE": true, "SOC_MCPWM_TIMERS_PER_GROUP": 3, "SOC_MCPWM_TRIGGERS_PER_OPERATOR": 2, "SOC_MEMSPI_IS_INDEPENDENT": true, "SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED": true, "SOC_MMU_DI_VADDR_SHARED": true, "SOC_MMU_LINEAR_ADDRESS_REGION_NUM": 1, "SOC_MMU_PAGE_SIZE_CONFIGURABLE": true, "SOC_MMU_PERIPH_NUM": 1, "SOC_MODEM_CLOCK_IS_INDEPENDENT": true, "SOC_MODEM_CLOCK_SUPPORTED": true, "SOC_MPI_MEM_BLOCKS_NUM": 4, "SOC_MPI_OPERATIONS_NUM": 3, "SOC_MPI_SUPPORTED": true, "SOC_MPU_MIN_REGION_SIZE": 536870912, "SOC_MPU_REGIONS_MAX_NUM": 8, "SOC_MWDT_SUPPORT_XTAL": true, "SOC_PARLIO_GROUPS": 1, "SOC_PARLIO_RX_UNITS_PER_GROUP": 1, "SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH": 16, "SOC_PARLIO_SUPPORTED": true, "SOC_PARLIO_TX_RX_SHARE_INTERRUPT": true, "SOC_PARLIO_TX_UNITS_PER_GROUP": 1, "SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH": 16, "SOC_PAU_SUPPORTED": true, "SOC_PCNT_CHANNELS_PER_UNIT": 2, "SOC_PCNT_GROUPS": 1, "SOC_PCNT_SUPPORTED": true, "SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE": true, "SOC_PCNT_THRES_POINT_PER_UNIT": 2, "SOC_PCNT_UNITS_PER_GROUP": 4, "SOC_PHY_COMBO_MODULE": true, "SOC_PHY_DIG_REGS_MEM_SIZE": 21, "SOC_PHY_SUPPORTED": true, "SOC_PMU_SUPPORTED": true, "SOC_PM_CPU_RETENTION_BY_SW": true, "SOC_PM_MODEM_RETENTION_BY_REGDMA": true, "SOC_PM_PAU_LINK_NUM": 4, "SOC_PM_RETENTION_HAS_CLOCK_BUG": true, "SOC_PM_SUPPORTED": true, "SOC_PM_SUPPORT_BEACON_WAKEUP": true, "SOC_PM_SUPPORT_BT_WAKEUP": true, "SOC_PM_SUPPORT_CPU_PD": true, "SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY": true, "SOC_PM_SUPPORT_EXT1_WAKEUP": true, "SOC_PM_SUPPORT_EXT1_WAKEUP_MODE_PER_PIN": true, "SOC_PM_SUPPORT_HP_AON_PD": true, "SOC_PM_SUPPORT_MAC_BB_PD": true, "SOC_PM_SUPPORT_MODEM_PD": true, "SOC_PM_SUPPORT_PMU_MODEM_STATE": true, "SOC_PM_SUPPORT_RC32K_PD": true, "SOC_PM_SUPPORT_RC_FAST_PD": true, "SOC_PM_SUPPORT_RTC_PERIPH_PD": true, "SOC_PM_SUPPORT_TOP_PD": true, "SOC_PM_SUPPORT_VDDSDIO_PD": true, "SOC_PM_SUPPORT_WIFI_WAKEUP": true, "SOC_PM_SUPPORT_XTAL32K_PD": true, "SOC_RCC_IS_INDEPENDENT": true, "SOC_RMT_CHANNELS_PER_GROUP": 4, "SOC_RMT_GROUPS": 1, "SOC_RMT_MEM_WORDS_PER_CHANNEL": 48, "SOC_RMT_RX_CANDIDATES_PER_GROUP": 2, "SOC_RMT_SUPPORTED": true, "SOC_RMT_SUPPORT_RC_FAST": true, "SOC_RMT_SUPPORT_RX_DEMODULATION": true, "SOC_RMT_SUPPORT_RX_PINGPONG": true, "SOC_RMT_SUPPORT_TX_ASYNC_STOP": true, "SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY": true, "SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP": true, "SOC_RMT_SUPPORT_TX_LOOP_COUNT": true, "SOC_RMT_SUPPORT_TX_SYNCHRO": true, "SOC_RMT_SUPPORT_XTAL": true, "SOC_RMT_TX_CANDIDATES_PER_GROUP": 2, "SOC_RNG_CLOCK_IS_INDEPENDENT": true, "SOC_RNG_SUPPORTED": true, "SOC_RSA_MAX_BIT_LEN": 3072, "SOC_RTCIO_HOLD_SUPPORTED": true, "SOC_RTCIO_INPUT_OUTPUT_SUPPORTED": true, "SOC_RTCIO_PIN_COUNT": 8, "SOC_RTCIO_VALID_RTCIO_MASK": 255, "SOC_RTCIO_WAKE_SUPPORTED": true, "SOC_RTC_FAST_MEM_SUPPORTED": true, "SOC_RTC_MEM_SUPPORTED": true, "SOC_SDIO_SLAVE_SUPPORTED": true, "SOC_SDM_CHANNELS_PER_GROUP": 4, "SOC_SDM_CLK_SUPPORT_PLL_F80M": true, "SOC_SDM_CLK_SUPPORT_XTAL": true, "SOC_SDM_GROUPS": 1, "SOC_SDM_SUPPORTED": true, "SOC_SECURE_BOOT_SUPPORTED": true, "SOC_SECURE_BOOT_V2_ECC": true, "SOC_SECURE_BOOT_V2_RSA": true, "SOC_SHARED_IDCACHE_SUPPORTED": true, "SOC_SHA_DMA_MAX_BUFFER_SIZE": 3968, "SOC_SHA_GDMA": true, "SOC_SHA_SUPPORTED": true, "SOC_SHA_SUPPORT_DMA": true, "SOC_SHA_SUPPORT_RESUME": true, "SOC_SHA_SUPPORT_SHA1": true, "SOC_SHA_SUPPORT_SHA224": true, "SOC_SHA_SUPPORT_SHA256": true, "SOC_SPI_FLASH_SUPPORTED": true, "SOC_SPI_MAXIMUM_BUFFER_SIZE": 64, "SOC_SPI_MAX_CS_NUM": 6, "SOC_SPI_MAX_PRE_DIVIDER": 16, "SOC_SPI_MEM_SUPPORT_AUTO_RESUME": true, "SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND": true, "SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE": true, "SOC_SPI_MEM_SUPPORT_CHECK_SUS": true, "SOC_SPI_MEM_SUPPORT_IDLE_INTR": true, "SOC_SPI_MEM_SUPPORT_SW_SUSPEND": true, "SOC_SPI_MEM_SUPPORT_WRAP": true, "SOC_SPI_PERIPH_NUM": 2, "SOC_SPI_SCT_BUFFER_NUM_MAX": true, "SOC_SPI_SCT_CONF_BITLEN_MAX": 262138, "SOC_SPI_SCT_REG_NUM": 14, "SOC_SPI_SCT_SUPPORTED": true, "SOC_SPI_SLAVE_SUPPORT_SEG_TRANS": true, "SOC_SPI_SUPPORT_CD_SIG": true, "SOC_SPI_SUPPORT_CLK_PLL_F80M": true, "SOC_SPI_SUPPORT_CLK_RC_FAST": true, "SOC_SPI_SUPPORT_CLK_XTAL": true, "SOC_SPI_SUPPORT_CONTINUOUS_TRANS": true, "SOC_SPI_SUPPORT_DDRCLK": true, "SOC_SPI_SUPPORT_SLAVE_HD_VER2": true, "SOC_SUPPORTS_SECURE_DL_MODE": true, "SOC_SUPPORT_COEXISTENCE": true, "SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY": true, "SOC_SYSTIMER_ALARM_MISS_COMPENSATE": true, "SOC_SYSTIMER_ALARM_NUM": 3, "SOC_SYSTIMER_BIT_WIDTH_HI": 20, "SOC_SYSTIMER_BIT_WIDTH_LO": 32, "SOC_SYSTIMER_COUNTER_NUM": 2, "SOC_SYSTIMER_FIXED_DIVIDER": true, "SOC_SYSTIMER_INT_LEVEL": true, "SOC_SYSTIMER_SUPPORTED": true, "SOC_SYSTIMER_SUPPORT_ETM": true, "SOC_SYSTIMER_SUPPORT_RC_FAST": true, "SOC_TEMPERATURE_SENSOR_INTR_SUPPORT": true, "SOC_TEMPERATURE_SENSOR_SUPPORT_ETM": true, "SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC": true, "SOC_TEMPERATURE_SENSOR_SUPPORT_XTAL": true, "SOC_TEMP_SENSOR_SUPPORTED": true, "SOC_TIMER_GROUPS": 2, "SOC_TIMER_GROUP_COUNTER_BIT_WIDTH": 54, "SOC_TIMER_GROUP_SUPPORT_RC_FAST": true, "SOC_TIMER_GROUP_SUPPORT_XTAL": true, "SOC_TIMER_GROUP_TIMERS_PER_GROUP": 1, "SOC_TIMER_GROUP_TOTAL_TIMERS": 2, "SOC_TIMER_SUPPORT_ETM": true, "SOC_TIMER_SUPPORT_SLEEP_RETENTION": true, "SOC_TWAI_BRP_MAX": 32768, "SOC_TWAI_BRP_MIN": 2, "SOC_TWAI_CLK_SUPPORT_XTAL": true, "SOC_TWAI_CONTROLLER_NUM": 2, "SOC_TWAI_SUPPORTED": true, "SOC_TWAI_SUPPORTS_RX_STATUS": true, "SOC_UART_BITRATE_MAX": 5000000, "SOC_UART_FIFO_LEN": 128, "SOC_UART_HAS_LP_UART": true, "SOC_UART_HP_NUM": 2, "SOC_UART_LP_NUM": 1, "SOC_UART_NUM": 3, "SOC_UART_SUPPORTED": true, "SOC_UART_SUPPORT_FSM_TX_WAIT_SEND": true, "SOC_UART_SUPPORT_PLL_F80M_CLK": true, "SOC_UART_SUPPORT_RTC_CLK": true, "SOC_UART_SUPPORT_SLEEP_RETENTION": true, "SOC_UART_SUPPORT_WAKEUP_INT": true, "SOC_UART_SUPPORT_XTAL_CLK": true, "SOC_ULP_LP_UART_SUPPORTED": true, "SOC_ULP_SUPPORTED": true, "SOC_USB_SERIAL_JTAG_SUPPORTED": true, "SOC_WDT_SUPPORTED": true, "SOC_WIFI_CSI_SUPPORT": true, "SOC_WIFI_FTM_SUPPORT": true, "SOC_WIFI_GCMP_SUPPORT": true, "SOC_WIFI_HE_SUPPORT": true, "SOC_WIFI_HW_TSF": true, "SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH": 12, "SOC_WIFI_MESH_SUPPORT": true, "SOC_WIFI_SUPPORTED": true, "SOC_WIFI_WAPI_SUPPORT": true, "SOC_XTAL_SUPPORT_40M": true, "SPIFFS_API_DBG": false, "SPIFFS_CACHE": true, "SPIFFS_CACHE_DBG": false, "SPIFFS_CACHE_STATS": false, "SPIFFS_CACHE_WR": true, "SPIFFS_CHECK_DBG": false, "SPIFFS_DBG": false, "SPIFFS_FOLLOW_SYMLINKS": false, "SPIFFS_GC_DBG": false, "SPIFFS_GC_MAX_RUNS": 10, "SPIFFS_GC_STATS": false, "SPIFFS_MAX_PARTITIONS": 3, "SPIFFS_META_LENGTH": 4, "SPIFFS_OBJ_NAME_LEN": 32, "SPIFFS_PAGE_CHECK": true, "SPIFFS_PAGE_SIZE": 256, "SPIFFS_TEST_VISUALISATION": false, "SPIFFS_USE_MAGIC": true, "SPIFFS_USE_MAGIC_LENGTH": true, "SPIFFS_USE_MTIME": true, "SPI_FLASH_BROWNOUT_RESET": true, "SPI_FLASH_BROWNOUT_RESET_XMC": true, "SPI_FLASH_BYPASS_BLOCK_ERASE": false, "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED": false, "SPI_FLASH_DANGEROUS_WRITE_ABORTS": true, "SPI_FLASH_DANGEROUS_WRITE_ALLOWED": false, "SPI_FLASH_DANGEROUS_WRITE_FAILS": false, "SPI_FLASH_ENABLE_COUNTERS": false, "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE": true, "SPI_FLASH_ERASE_YIELD_DURATION_MS": 20, "SPI_FLASH_ERASE_YIELD_TICKS": 1, "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST": false, "SPI_FLASH_ROM_DRIVER_PATCH": true, "SPI_FLASH_ROM_IMPL": false, "SPI_FLASH_SIZE_OVERRIDE": false, "SPI_FLASH_SUPPORT_BOYA_CHIP": false, "SPI_FLASH_SUPPORT_GD_CHIP": false, "SPI_FLASH_SUPPORT_ISSI_CHIP": false, "SPI_FLASH_SUPPORT_MXIC_CHIP": false, "SPI_FLASH_SUPPORT_TH_CHIP": false, "SPI_FLASH_SUPPORT_WINBOND_CHIP": false, "SPI_FLASH_SUSPEND_TSUS_VAL_US": 50, "SPI_FLASH_VENDOR_XMC_SUPPORTED": true, "SPI_FLASH_VERIFY_WRITE": false, "SPI_FLASH_WRITE_CHUNK_SIZE": 8192, "SPI_FLASH_YIELD_DURING_ERASE": true, "SPI_MASTER_IN_IRAM": false, "SPI_MASTER_ISR_IN_IRAM": true, "SPI_SLAVE_IN_IRAM": false, "SPI_SLAVE_ISR_IN_IRAM": true, "TEMP_SENSOR_ENABLE_DEBUG_LOG": false, "TEMP_SENSOR_ISR_IRAM_SAFE": false, "TEMP_SENSOR_SUPPRESS_DEPRECATE_WARN": false, "TWAI_ISR_IN_IRAM": false, "UART_HW_FLOWCTRL_CTS_RTS": false, "UART_HW_FLOWCTRL_DISABLE": true, "UART_ISR_IN_IRAM": false, "ULP_COPROC_ENABLED": false, "UNITY_ENABLE_64BIT": false, "UNITY_ENABLE_BACKTRACE_ON_FAIL": false, "UNITY_ENABLE_COLOR": false, "UNITY_ENABLE_DOUBLE": true, "UNITY_ENABLE_FIXTURE": false, "UNITY_ENABLE_FLOAT": true, "UNITY_ENABLE_IDF_TEST_RUNNER": true, "USJ_ENABLE_USB_SERIAL_JTAG": true, "VFS_MAX_COUNT": 8, "VFS_SELECT_IN_RAM": false, "VFS_SEMIHOSTFS_MAX_MOUNT_POINTS": 1, "VFS_SUPPORT_DIR": true, "VFS_SUPPORT_IO": true, "VFS_SUPPORT_SELECT": true, "VFS_SUPPORT_TERMIOS": true, "VFS_SUPPRESS_SELECT_DEBUG_OUTPUT": true, "WIFI_PROV_AUTOSTOP_TIMEOUT": 30, "WIFI_PROV_BLE_BONDING": false, "WIFI_PROV_BLE_FORCE_ENCRYPTION": false, "WIFI_PROV_BLE_SEC_CONN": true, "WIFI_PROV_KEEP_BLE_ON_AFTER_PROV": false, "WIFI_PROV_SCAN_MAX_ENTRIES": 16, "WIFI_PROV_STA_ALL_CHANNEL_SCAN": true, "WIFI_PROV_STA_FAST_SCAN": false, "WL_SECTOR_SIZE": 4096, "WL_SECTOR_SIZE_4096": true, "WL_SECTOR_SIZE_512": false, "WS_BUFFER_SIZE": 1024, "WS_DYNAMIC_BUFFER": false, "WS_TRANSPORT": true, "XTAL_FREQ": 40, "XTAL_FREQ_40": true}