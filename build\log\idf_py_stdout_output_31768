-- ccache will be used for faster recompilation
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32c6
Processing 4 dependencies:
[1/4] espressif/cmake_utilities (0.5.3)
[2/4] espressif/esp_lcd_sh8601 (1.0.0)
[3/4] lvgl/lvgl (8.4.0)
[4/4] idf (5.3.1)
-- Project sdkconfig file E:/APPprj/LinkPet/linkpet-esp/sdkconfig
Loading defaults file E:/APPprj/LinkPet/linkpet-esp/sdkconfig.defaults...
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/ld/rom.api.ld
-- App "09_LVGL_V8_Test" version: 1
-- Adding linker script E:/APPprj/LinkPet/linkpet-esp/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script E:/APPprj/LinkPet/linkpet-esp/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.api.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.rvfp.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.wdt.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.systimer.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.version.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.phy.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.coexist.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.net80211.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.pp.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.newlib.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.newlib-normal.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6/ld/esp32c6.rom.heap.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/ld/esp32c6.peripherals.ld
-- ESP_LCD_SH8601: 1.0.0
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__cmake_utilities espressif__esp_lcd_sh8601 esptool_py fatfs freertos gif_player hal heap http_parser i2c_bsp idf_test ieee802154 json log lvgl__lvgl lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table protobuf-c protocomm pthread riscv sdmmc soc spi_flash spiffs tcp_transport touch_bsp ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant
-- Component paths: D:/Espressif/frameworks/esp-idf-v5.3.1/components/app_trace D:/Espressif/frameworks/esp-idf-v5.3.1/components/app_update D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support D:/Espressif/frameworks/esp-idf-v5.3.1/components/bt D:/Espressif/frameworks/esp-idf-v5.3.1/components/cmock D:/Espressif/frameworks/esp-idf-v5.3.1/components/console D:/Espressif/frameworks/esp-idf-v5.3.1/components/cxx D:/Espressif/frameworks/esp-idf-v5.3.1/components/driver D:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp-tls D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_adc D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_coex D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_ana_cmpr D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_cam D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_dac D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_gpio D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_gptimer D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_i2c D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_i2s D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_isp D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_jpeg D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_ledc D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_mcpwm D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_parlio D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_pcnt D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_ppa D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_rmt D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_sdio D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_sdm D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_sdmmc D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_sdspi D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_spi D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_touch_sens D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_tsens D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_uart D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_driver_usb_serial_jtag D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_eth D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_event D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_gdbstub D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hid D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_http_client D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_http_server D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_https_ota D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_https_server D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_lcd D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_local_ctrl D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_mm D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_netif D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_netif_stack D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_partition D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_phy D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_pm D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_psram D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_ringbuf D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_system D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_timer D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_vfs_console D:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_wifi D:/Espressif/frameworks/esp-idf-v5.3.1/components/espcoredump E:/APPprj/LinkPet/linkpet-esp/managed_components/espressif__cmake_utilities E:/APPprj/LinkPet/linkpet-esp/managed_components/espressif__esp_lcd_sh8601 D:/Espressif/frameworks/esp-idf-v5.3.1/components/esptool_py D:/Espressif/frameworks/esp-idf-v5.3.1/components/fatfs D:/Espressif/frameworks/esp-idf-v5.3.1/components/freertos E:/APPprj/LinkPet/linkpet-esp/components/gif_player D:/Espressif/frameworks/esp-idf-v5.3.1/components/hal D:/Espressif/frameworks/esp-idf-v5.3.1/components/heap D:/Espressif/frameworks/esp-idf-v5.3.1/components/http_parser E:/APPprj/LinkPet/linkpet-esp/components/i2c_bsp D:/Espressif/frameworks/esp-idf-v5.3.1/components/idf_test D:/Espressif/frameworks/esp-idf-v5.3.1/components/ieee802154 D:/Espressif/frameworks/esp-idf-v5.3.1/components/json D:/Espressif/frameworks/esp-idf-v5.3.1/components/log E:/APPprj/LinkPet/linkpet-esp/managed_components/lvgl__lvgl D:/Espressif/frameworks/esp-idf-v5.3.1/components/lwip E:/APPprj/LinkPet/linkpet-esp/main D:/Espressif/frameworks/esp-idf-v5.3.1/components/mbedtls D:/Espressif/frameworks/esp-idf-v5.3.1/components/mqtt D:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib D:/Espressif/frameworks/esp-idf-v5.3.1/components/nvs_flash D:/Espressif/frameworks/esp-idf-v5.3.1/components/nvs_sec_provider D:/Espressif/frameworks/esp-idf-v5.3.1/components/openthread D:/Espressif/frameworks/esp-idf-v5.3.1/components/partition_table D:/Espressif/frameworks/esp-idf-v5.3.1/components/protobuf-c D:/Espressif/frameworks/esp-idf-v5.3.1/components/protocomm D:/Espressif/frameworks/esp-idf-v5.3.1/components/pthread D:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv D:/Espressif/frameworks/esp-idf-v5.3.1/components/sdmmc D:/Espressif/frameworks/esp-idf-v5.3.1/components/soc D:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash D:/Espressif/frameworks/esp-idf-v5.3.1/components/spiffs D:/Espressif/frameworks/esp-idf-v5.3.1/components/tcp_transport E:/APPprj/LinkPet/linkpet-esp/components/touch_bsp D:/Espressif/frameworks/esp-idf-v5.3.1/components/ulp D:/Espressif/frameworks/esp-idf-v5.3.1/components/unity D:/Espressif/frameworks/esp-idf-v5.3.1/components/usb D:/Espressif/frameworks/esp-idf-v5.3.1/components/vfs D:/Espressif/frameworks/esp-idf-v5.3.1/components/wear_levelling D:/Espressif/frameworks/esp-idf-v5.3.1/components/wifi_provisioning D:/Espressif/frameworks/esp-idf-v5.3.1/components/wpa_supplicant
-- Configuring done
-- Generating done
-- Build files have been written to: E:/APPprj/LinkPet/linkpet-esp/build
