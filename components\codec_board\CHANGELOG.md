# Changelog

## v0.5.5

- Add board support for `ESP32_S3_KORVO_2L`, `ESP32_S3_EchoEar`, `ATOMS3_ECHO_BASE`
- Mount SDcard use high speed for better performance on esp32s3 and esp32p4

## v0.5.4

- Add PDM support
- Add play and record support for `XD_AIOT_C3` and `ESP_SPOT`

## v0.5.3

- Add support for mount SDCard on esp32
- Add support for I2C manually control use `init_i2c` and `deinit_i2c`
- Fixed I2S driver failed to initialized if more than one

## v0.5.0

- Initial version of `codec_board`
