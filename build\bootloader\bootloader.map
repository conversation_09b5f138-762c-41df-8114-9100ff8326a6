Archive member included to satisfy reference by file (symbol)

esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                              (esp_bootloader_desc)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                              (__assert_func)
esp-idf/main/libmain.a(bootloader_start.c.obj)
                              (call_start_cpu0)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (bootloader_utility_load_partition_table)
esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (esp_partition_table_verify)
esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_load_image)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_console_deinit)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_sha256_start)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_ana_clock_glitch_reset_config)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (bootloader_init)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_common_ota_select_crc)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj) (bootloader_clock_configure)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj) (bootloader_init_mem)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj) (bootloader_fill_random)
esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (esp_flash_encryption_enabled)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_random_disable)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_mmap_get_free_pages)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj) (bootloader_flash_update_id)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj) (bootloader_clear_bss_section)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj) (bootloader_console_init)
esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (ESP_EFUSE_DIS_DIRECT_BOOT)
esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_enable_rom_secure_download_mode)
esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_read_field_blob)
esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                              esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj) (esp_efuse_utility_process)
esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_get_key_dis_read)
esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (esp_efuse_utility_clear_program_registers)
esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj) (esp_cpu_configure_region_protection)
esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj) (rtc_clk_init)
esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj) (rtc_clk_32k_enable)
esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj) (get_act_hp_dbias)
esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (esp_rom_regi2c_write)
esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (wdt_hal_init)
esp-idf/log/liblog.a(log_noos.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (esp_log_timestamp)
esp-idf/hal/libhal.a(efuse_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj) (efuse_hal_chip_revision)
esp-idf/hal/libhal.a(efuse_hal.c.obj)
                              esp-idf/hal/libhal.a(efuse_hal.c.obj) (efuse_hal_get_major_chip_version)
esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj) (lp_timer_hal_get_cycle_count)
esp-idf/hal/libhal.a(mmu_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (mmu_hal_unmap_all)
esp-idf/hal/libhal.a(cache_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj) (cache_hal_init)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__lshrdi3)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__ashldi3)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__popcountsi2)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (__divdi3)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (__udivdi3)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
                              D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o) (__clz_tab)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj) (memcmp)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (_impure_data)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (memset)
D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (memcpy)

Discarded input sections

 .text          0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c6.c.obj
 .data          0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c6.c.obj
 .bss           0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c6.c.obj
 .comment       0x00000000       0x30 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c6.c.obj
 .riscv.attributes
                0x00000000       0x49 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c6.c.obj
 .text          0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .text          0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .data          0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .bss           0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .text.__getreent
                0x00000000        0xa esp-idf/main/libmain.a(bootloader_start.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_common_get_partition_description
                0x00000000       0x9e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_atexit
                0x00000000        0x8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_sha256_hex_to_str
                0x00000000       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_sha256_flash_contents
                0x00000000       0xba esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_load_image_no_verify
                0x00000000        0xe esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify
                0x00000000        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_get_metadata
                0x00000000       0xc2 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify_bootloader_data
                0x00000000       0x26 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify_bootloader
                0x00000000       0x2c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_get_flash_size
                0x00000000       0x52 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_write_protect_crypt_cnt
                0x00000000       0x10 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_get_flash_encryption_mode
                0x00000000       0x8e esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .rodata.esp_flash_encryption_set_release_mode.str1.4
                0x00000000       0xb6 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_encryption_set_release_mode
                0x00000000      0x124 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .rodata.esp_flash_encryption_cfg_verify_release_mode.str1.4
                0x00000000      0x373 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_encryption_cfg_verify_release_mode
                0x00000000      0x2ea esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_flash_erase_range
                0x00000000       0x7e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_spi_flash_reset
                0x00000000       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.7       0x00000000       0x88 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.8       0x00000000        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .text.bootloader_flash_update_size
                0x00000000        0xc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .iram1.1       0x00000000       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SYS_DATA_PART2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USER_DATA_MAC_CUSTOM
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USER_DATA
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH6
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_OCODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_TEMP_CALIB
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_OPTIONAL_UNIQUE_ID
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_VENDOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_TEMP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_CAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_BLK_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PKG_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DBIAS_VOL_GAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DSLP_LP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DSLP_LP_DBG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LSLP_HP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LSLP_HP_DBG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ACTIVE_LP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ACTIVE_HP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data.ESP_EFUSE_MAC_EXT
                0x00000000        0xc esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data.ESP_EFUSE_MAC
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FORCE_SEND_RESUME
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_UART_PRINT_CONTROL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DIRECT_BOOT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_TPUW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_CRYPT_DPA_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SEC_DPA_LEVEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WDT_DELAY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_VDD_SPI_AS_GPIO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USB_EXCHG_PINS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_PAD_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SOFT_DIS_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_JTAG_SEL_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_TWAI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_FORCE_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SWAP_UART_SDIO_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_SYS_DATA2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SOFT_DIS_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_VDD_SPI_AS_GPIO
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_USB_EXCHG_PINS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_SYS_DATA2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_CUSTOM_MAC
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_USR_DATA
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH6
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_OCODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_TEMP_CALIB
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_OPTIONAL_UNIQUE_ID
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SYS_DATA_PART1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_VENDOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TEMP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_CAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PKG_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DBIAS_VOL_GAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DSLP_LP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DSLP_LP_DBG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LSLP_HP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LSLP_HP_DBG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ACTIVE_LP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ACTIVE_HP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_MAC_EXT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_MAC
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TPUW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SEC_DPA_LEVEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WDT_DELAY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_PAD_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_JTAG_SEL_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_TWAI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_FORCE_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SWAP_UART_SDIO_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_CRYPT_DPA_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RD_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SYS_DATA_PART2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY5  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY4  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY3  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY2  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY1  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY0  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USER_DATA_MAC_CUSTOM
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USER_DATA
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH6
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0_CH0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.OCODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.TEMP_CALIB
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.OPTIONAL_UNIQUE_ID
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_VENDOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_TEMP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_CAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.BLK_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PKG_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DBIAS_VOL_GAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DSLP_LP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DSLP_LP_DBG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LSLP_HP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LSLP_HP_DBG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ACTIVE_LP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ACTIVE_HP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.MAC_EXT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .rodata.MAC    0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FORCE_SEND_RESUME
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.UART_PRINT_CONTROL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DIRECT_BOOT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_TPUW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.CRYPT_DPA_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SEC_DPA_LEVEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WDT_DELAY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.VDD_SPI_AS_GPIO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USB_EXCHG_PINS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_PAD_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SOFT_DIS_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.JTAG_SEL_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_TWAI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_FORCE_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_SERIAL_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SWAP_UART_SDIO_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_SYS_DATA2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SOFT_DIS_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_VDD_SPI_AS_GPIO
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_USB_EXCHG_PINS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_SYS_DATA2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_CUSTOM_MAC
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_USR_DATA
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH6
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0_CH0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_OCODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_TEMP_CALIB
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_OPTIONAL_UNIQUE_ID
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SYS_DATA_PART1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_VENDOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_TEMP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_CAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PKG_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DBIAS_VOL_GAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DSLP_LP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DSLP_LP_DBG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LSLP_HP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LSLP_HP_DBG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ACTIVE_LP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ACTIVE_HP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_MAC_EXT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_MAC
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_TPUW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SEC_DPA_LEVEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WDT_DELAY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_PAD_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_JTAG_SEL_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_TWAI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_FORCE_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SWAP_UART_SDIO_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_CRYPT_DPA_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RD_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_info    0x00000000     0x2556 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_abbrev  0x00000000      0x106 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_aranges
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_line    0x00000000      0x21e esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_str     0x00000000     0x2815 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .riscv.attributes
                0x00000000       0x49 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_get_pkg_ver
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_set_rom_log_scheme
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_disable_rom_download_mode
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_enable_rom_secure_download_mode
                0x00000000       0x34 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_info    0x00000000      0x3ba esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_abbrev  0x00000000      0x18f esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_loc     0x00000000       0x1f esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_aranges
                0x00000000       0x38 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_ranges  0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_line    0x00000000      0x48f esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_str     0x00000000      0x5f3 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_frame   0x00000000       0x80 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .riscv.attributes
                0x00000000       0x49 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_blob
                0x00000000       0x6e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_read_field_bit.str1.4
                0x00000000       0x3b esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_bit
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_cnt
                0x00000000       0x50 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_blob
                0x00000000       0x74 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_write_field_cnt.str1.4
                0x00000000       0x4e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_cnt
                0x00000000       0xac esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_bit
                0x00000000       0x5a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_get_field_size
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_reg
                0x00000000       0x5a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_block
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_reg
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_block
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_begin.str1.4
                0x00000000       0x51 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_begin
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_cancel.str1.4
                0x00000000       0x5f esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_cancel
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_commit.str1.4
                0x00000000       0x37 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_commit
                0x00000000       0x8e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_check_errors
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_destroy_block.str1.4
                0x00000000      0x120 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_destroy_block
                0x00000000      0x13c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.0
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.1
                0x00000000       0x13 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.2
                0x00000000       0x19 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .sbss.s_batch_writing_mode
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_info    0x00000000     0x11af esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_abbrev  0x00000000      0x3cb esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_loc     0x00000000      0xa5b esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_aranges
                0x00000000       0x98 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_ranges  0x00000000       0xe0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_line    0x00000000     0x11c7 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_str     0x00000000      0x8fe esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_frame   0x00000000      0x220 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .riscv.attributes
                0x00000000       0x49 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.write_reg.str1.4
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.write_reg
                0x00000000       0x7a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_process.str1.4
                0x00000000       0x5d esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_process
                0x00000000      0x18a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_reset
                0x00000000       0x50 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_efuses
                0x00000000       0x2e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_erase_virt_blocks
                0x00000000        0x2 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_update_virt_blocks.str1.4
                0x00000000       0x27 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_update_virt_blocks
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_debug_dump_single_block.str1.4
                0x00000000       0x12 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_single_block
                0x00000000       0x8e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_pending
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_debug_dump_blocks.str1.4
                0x00000000        0xd esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_blocks
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_get_number_of_items
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_read_reg
                0x00000000       0x68 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_fill_buff
                0x00000000       0xde esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_count_once
                0x00000000       0x62 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_write_cnt.str1.4
                0x00000000       0x31 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_cnt
                0x00000000       0xce esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_write_reg.str1.4
                0x00000000       0x53 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_reg
                0x00000000       0x64 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_blob
                0x00000000       0x92 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_get_read_register_address.str1.4
                0x00000000       0x16 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_get_read_register_address
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_is_correct_written_data.str1.4
                0x00000000       0xba esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_is_correct_written_data
                0x00000000       0xd6 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.0
                0x00000000       0x2c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.1
                0x00000000       0x1b esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.2
                0x00000000        0xa esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.3
                0x00000000        0xf esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.4
                0x00000000       0x1a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .sbss.s_burn_counter
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_info    0x00000000     0x14af esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_abbrev  0x00000000      0x437 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_loc     0x00000000     0x145d esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_aranges
                0x00000000       0xa8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_ranges  0x00000000      0x2c8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_line    0x00000000     0x181a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_str     0x00000000      0xa49 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_frame   0x00000000      0x2bc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .riscv.attributes
                0x00000000       0x49 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_block_is_empty
                0x00000000       0x36 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_write_protect
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_read_protect
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_coding_scheme
                0x00000000        0x6 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_purpose_field
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_get_key_dis_read.str1.4
                0x00000000       0x8f esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_dis_read
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_dis_read
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_dis_write
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_dis_write
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_purpose
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_purpose
                0x00000000       0x3c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_keypurpose_dis_write
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_keypurpose_dis_write
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_find_purpose
                0x00000000       0x42 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_key_block_unused
                0x00000000       0x5e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_find_unused_key_block
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_count_unused_key_blocks
                0x00000000       0x32 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_write_key.str1.4
                0x00000000       0x65 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_write_key
                0x00000000      0x116 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_write_keys.str1.4
                0x00000000       0xd2 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_write_keys
                0x00000000      0x156 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_get_digest_revoke.str1.4
                0x00000000       0x42 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_digest_revoke
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_digest_revoke
                0x00000000       0x26 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_write_protect_of_digest_revoke
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_write_protect_of_digest_revoke
                0x00000000       0x26 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_secure_boot_read_key_digests.str1.4
                0x00000000       0x24 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_secure_boot_read_key_digests
                0x00000000       0xa8 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.0
                0x00000000       0x21 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.1
                0x00000000       0x2d esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.2
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.3
                0x00000000       0x23 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.4
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.5
                0x00000000       0x1b esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.s_revoke_table
                0x00000000       0x24 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.s_table
                0x00000000       0x78 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_info    0x00000000     0x1594 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_abbrev  0x00000000      0x3b8 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_loc     0x00000000      0xdce esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_aranges
                0x00000000       0xe0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_ranges  0x00000000      0x210 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_line    0x00000000     0x14b5 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_str     0x00000000     0x103b esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_frame   0x00000000      0x324 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .riscv.attributes
                0x00000000       0x49 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_clear_program_registers
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_check_errors
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_burn_chip_opt.str1.4
                0x00000000      0x1b0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_chip_opt
                0x00000000      0x2bc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_chip
                0x00000000        0xc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_apply_new_coding_scheme.str1.4
                0x00000000       0x3f esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_apply_new_coding_scheme
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.range_write_addr_blocks
                0x00000000       0x58 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss.write_mass_blocks
                0x00000000      0x160 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.range_read_addr_blocks
                0x00000000       0x58 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_info    0x00000000      0x8d7 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_abbrev  0x00000000      0x287 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_loc     0x00000000      0x35a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_aranges
                0x00000000       0x40 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_ranges  0x00000000      0x120 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_line    0x00000000      0xbd5 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_str     0x00000000      0x7a1 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_frame   0x00000000       0xdc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .riscv.attributes
                0x00000000       0x49 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_bbpll_add_consumer
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_bbpll_remove_consumer
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_bootstrap
                0x00000000        0x8 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enabled
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_8m_enabled
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_fast_src_get
                0x00000000        0xe esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_pll_and_pll_lock_release
                0x00000000       0x24 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_config_fast
                0x00000000       0x44 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_set_to_default_config
                0x00000000       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_xtal
                0x00000000       0x28 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_clk8m_enable
                0x00000000       0x1c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_clk8m_disable
                0x00000000       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_8m_enabled
                0x00000000        0xe esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.pmu_hp_system_power_param_default.str1.4
                0x00000000       0x55 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .text.pmu_hp_system_power_param_default
                0x00000000       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.pmu_hp_system_clock_param_default.str1.4
                0x00000000       0x1c esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .text.pmu_hp_system_clock_param_default
                0x00000000       0x3c esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.pmu_hp_system_digital_param_default.str1.4
                0x00000000       0x1e esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .text.pmu_hp_system_digital_param_default
                0x00000000       0x3c esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.pmu_hp_system_analog_param_default.str1.4
                0x00000000       0x1d esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .text.pmu_hp_system_analog_param_default
                0x00000000       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.pmu_hp_system_retention_param_default.str1.4
                0x00000000       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .text.pmu_hp_system_retention_param_default
                0x00000000       0x3c esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.pmu_lp_system_power_param_default.str1.4
                0x00000000       0x1c esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .text.pmu_lp_system_power_param_default
                0x00000000       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.pmu_lp_system_analog_param_default.str1.4
                0x00000000       0x1d esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .text.pmu_lp_system_analog_param_default
                0x00000000       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.lp_analog.0
                0x00000000       0x18 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.__func__.1
                0x00000000       0x23 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.lp_power.2
                0x00000000       0x18 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.__func__.3
                0x00000000       0x22 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.hp_retention.4
                0x00000000       0x18 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.__func__.5
                0x00000000       0x26 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.hp_analog.6
                0x00000000       0x24 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.__func__.7
                0x00000000       0x23 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.hp_digital.8
                0x00000000        0xc esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.__func__.9
                0x00000000       0x24 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.hp_clock.10
                0x00000000       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.__func__.11
                0x00000000       0x22 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.hp_power.12
                0x00000000       0x24 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .rodata.__func__.13
                0x00000000       0x22 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .iram1.1       0x00000000       0x4e esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .iram1.2       0x00000000       0x9c esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .rodata.__func__.0
                0x00000000       0x16 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .text.wdt_hal_deinit
                0x00000000       0x84 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .text          0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .data          0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .bss           0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.esp_log_impl_lock.str1.4
                0x00000000       0x2c esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_lock
                0x00000000       0x38 esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_lock_timeout
                0x00000000       0x14 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.esp_log_impl_unlock.str1.4
                0x00000000        0xc esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_unlock
                0x00000000       0x3a esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.__func__.1
                0x00000000       0x14 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.__func__.0
                0x00000000       0x12 esp-idf/log/liblog.a(log_noos.c.obj)
 .sbss.s_lock   0x00000000        0x4 esp-idf/log/liblog.a(log_noos.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_get_mac
                0x00000000       0x14 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_set_timing
                0x00000000       0x44 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_read
                0x00000000       0x4c esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_clear_program_registers
                0x00000000        0x8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_program
                0x00000000       0x6a esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_rs_calculate
                0x00000000        0x8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_is_coding_error_in_block
                0x00000000       0x54 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .iram1.1       0x00000000       0x42 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .iram1.3       0x00000000       0x14 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .iram1.4       0x00000000       0x14 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_bytes_to_pages
                0x00000000       0x36 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_paddr_to_vaddr
                0x00000000       0xea esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_unmap_region
                0x00000000       0xa6 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_vaddr_to_paddr
                0x00000000       0xf2 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.s_get_cache_state
                0x00000000       0x3e esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_suspend
                0x00000000       0x2e esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_resume
                0x00000000       0x36 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_is_cache_enabled
                0x00000000        0x8 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_vaddr_to_cache_level_id
                0x00000000       0x32 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_invalidate_addr
                0x00000000       0x3c esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_freeze
                0x00000000       0x1c esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_unfreeze
                0x00000000       0x1a esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_get_cache_line_size
                0x00000000       0x22 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text          0x00000000       0x28 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_info    0x00000000      0x1af D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_abbrev  0x00000000      0x10c D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_loclists
                0x00000000       0x6b D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_aranges
                0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_line    0x00000000      0x107 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_str     0x00000000      0x1de D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_line_str
                0x00000000      0x212 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_frame   0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .text          0x00000000       0x28 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_info    0x00000000      0x1af D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_abbrev  0x00000000      0x10c D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_loclists
                0x00000000       0x6b D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_aranges
                0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_line    0x00000000      0x107 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_str     0x00000000      0x1de D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_line_str
                0x00000000      0x212 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_frame   0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .text          0x00000000       0x42 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_info    0x00000000       0xdf D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_abbrev  0x00000000       0x65 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_loclists
                0x00000000       0xd6 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_aranges
                0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_line    0x00000000       0xe9 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_str     0x00000000      0x1a5 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_line_str
                0x00000000      0x212 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_frame   0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .text          0x00000000      0x39e D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_info    0x00000000      0x7a1 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_abbrev  0x00000000      0x1bf D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_loclists
                0x00000000      0x5ab D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_aranges
                0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_rnglists
                0x00000000       0x93 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_line    0x00000000      0x9ae D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_str     0x00000000      0x253 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_line_str
                0x00000000      0x212 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .eh_frame      0x00000000       0x28 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .text          0x00000000      0x35e D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_info    0x00000000      0x76f D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x00000000      0x1a6 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_loclists
                0x00000000      0x6ff D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_rnglists
                0x00000000       0x99 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_line    0x00000000      0x905 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_str     0x00000000      0x254 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_line_str
                0x00000000      0x212 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .eh_frame      0x00000000       0x28 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .text          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .rodata        0x00000000      0x100 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_info    0x00000000       0xe6 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_abbrev  0x00000000       0x70 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_aranges
                0x00000000       0x18 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_line    0x00000000       0x3f D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_str     0x00000000      0x1a1 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_line_str
                0x00000000      0x212 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .text          0x00000000       0x4a D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_info    0x00000000      0x10f D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_abbrev  0x00000000       0x8a D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_loclists
                0x00000000      0x130 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_aranges
                0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_line    0x00000000      0x15f D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_str     0x00000000      0x113 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_line_str
                0x00000000      0x2eb D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_frame   0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .text          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .data          0x00000000       0xf0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .sdata         0x00000000        0x4 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_info    0x00000000      0x84c D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_abbrev  0x00000000      0x174 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_aranges
                0x00000000       0x18 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_line    0x00000000       0x51 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_str     0x00000000      0x4e3 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_line_str
                0x00000000      0x2f3 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .text          0x00000000       0xa8 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_line    0x00000000      0x18e D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_line_str
                0x00000000      0x10c D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_info    0x00000000       0x33 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_abbrev  0x00000000       0x28 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_aranges
                0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_str     0x00000000      0x11f D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .riscv.attributes
                0x00000000       0x47 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .text          0x00000000       0xe8 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .data          0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .bss           0x00000000        0x0 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_info    0x00000000      0x256 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_abbrev  0x00000000      0x107 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_loclists
                0x00000000      0x1d6 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_aranges
                0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_line    0x00000000      0x31c D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_str     0x00000000      0x134 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_line_str
                0x00000000      0x3b8 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .comment       0x00000000       0x30 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_frame   0x00000000       0x20 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .riscv.attributes
                0x00000000       0x49 D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)

Memory Configuration

Name             Origin             Length             Attributes
iram_seg         0x4086c110         0x00002500         xrw
iram_loader_seg  0x4086e610         0x00007000         xrw
dram_seg         0x40875610         0x00005000         rw
*default*        0x00000000         0xffffffff

Linker script and memory map

                0x00000000                        IDF_TARGET_ESP32C6 = 0x0
LOAD CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c6.c.obj
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/main/libmain.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a
LOAD D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libnosys.a
LOAD D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a
START GROUP
LOAD D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a
LOAD D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libnosys.a
END GROUP
                [!provide]                        PROVIDE (esprv_int_set_priority = esprv_intc_int_set_priority)
                [!provide]                        PROVIDE (esprv_int_set_threshold = esprv_intc_int_set_threshold)
                [!provide]                        PROVIDE (esprv_int_enable = esprv_intc_int_enable)
                [!provide]                        PROVIDE (esprv_int_disable = esprv_intc_int_disable)
                [!provide]                        PROVIDE (esprv_int_set_type = esprv_intc_int_set_type)
                0x40000018                        rtc_get_reset_reason = 0x40000018
                0x4000001c                        analog_super_wdt_reset_happened = 0x4000001c
                0x40000020                        rtc_get_wakeup_cause = 0x40000020
                0x40000024                        rtc_unhold_all_pads = 0x40000024
                0x40000028                        ets_printf = 0x40000028
                0x4000002c                        ets_install_putc1 = 0x4000002c
                0x40000030                        ets_install_putc2 = 0x40000030
                0x40000034                        ets_install_uart_printf = 0x40000034
                0x40000038                        ets_install_usb_printf = 0x40000038
                0x4000003c                        ets_get_printf_channel = 0x4000003c
                0x40000040                        ets_delay_us = 0x40000040
                0x40000044                        ets_get_cpu_frequency = 0x40000044
                0x40000048                        ets_update_cpu_frequency = 0x40000048
                0x4000004c                        ets_install_lock = 0x4000004c
                0x40000050                        UartRxString = 0x40000050
                0x40000054                        UartGetCmdLn = 0x40000054
                0x40000058                        uart_tx_one_char = 0x40000058
                0x4000005c                        uart_tx_one_char2 = 0x4000005c
                0x40000060                        uart_rx_one_char = 0x40000060
                0x40000064                        uart_rx_one_char_block = 0x40000064
                0x40000068                        uart_rx_intr_handler = 0x40000068
                0x4000006c                        uart_rx_readbuff = 0x4000006c
                0x40000070                        uartAttach = 0x40000070
                0x40000074                        uart_tx_flush = 0x40000074
                0x40000078                        uart_tx_wait_idle = 0x40000078
                0x4000007c                        uart_div_modify = 0x4000007c
                0x40000080                        ets_write_char_uart = 0x40000080
                0x40000084                        uart_tx_switch = 0x40000084
                0x40000088                        roundup2 = 0x40000088
                0x4000008c                        multofup = 0x4000008c
                0x40000090                        software_reset = 0x40000090
                0x40000094                        software_reset_cpu = 0x40000094
                0x40000098                        ets_clk_assist_debug_clock_enable = 0x40000098
                0x4000009c                        clear_super_wdt_reset_flag = 0x4000009c
                0x400000a0                        disable_default_watchdog = 0x400000a0
                0x400000a4                        esp_rom_set_rtc_wake_addr = 0x400000a4
                0x400000a8                        esp_rom_get_rtc_wake_addr = 0x400000a8
                0x400000ac                        send_packet = 0x400000ac
                0x400000b0                        recv_packet = 0x400000b0
                0x400000b4                        GetUartDevice = 0x400000b4
                0x400000b8                        UartDwnLdProc = 0x400000b8
                0x400000bc                        GetSecurityInfoProc = 0x400000bc
                0x400000c0                        Uart_Init = 0x400000c0
                0x400000c4                        ets_set_user_start = 0x400000c4
                0x4004fffc                        ets_rom_layout_p = 0x4004fffc
                0x4087fff8                        ets_ops_table_ptr = 0x4087fff8
                0x4087fffc                        g_saved_pc = 0x4087fffc
                0x400000c8                        mz_adler32 = 0x400000c8
                0x400000cc                        mz_free = 0x400000cc
                0x400000d0                        tdefl_compress = 0x400000d0
                0x400000d4                        tdefl_compress_buffer = 0x400000d4
                0x400000d8                        tdefl_compress_mem_to_heap = 0x400000d8
                0x400000dc                        tdefl_compress_mem_to_mem = 0x400000dc
                0x400000e0                        tdefl_compress_mem_to_output = 0x400000e0
                0x400000e4                        tdefl_get_adler32 = 0x400000e4
                0x400000e8                        tdefl_get_prev_return_status = 0x400000e8
                0x400000ec                        tdefl_init = 0x400000ec
                0x400000f0                        tdefl_write_image_to_png_file_in_memory = 0x400000f0
                0x400000f4                        tdefl_write_image_to_png_file_in_memory_ex = 0x400000f4
                0x400000f8                        tinfl_decompress = 0x400000f8
                0x400000fc                        tinfl_decompress_mem_to_callback = 0x400000fc
                0x40000100                        tinfl_decompress_mem_to_heap = 0x40000100
                0x40000104                        tinfl_decompress_mem_to_mem = 0x40000104
                0x40000108                        jd_prepare = 0x40000108
                0x4000010c                        jd_decomp = 0x4000010c
                0x40000110                        esp_rom_spiflash_wait_idle = 0x40000110
                0x40000114                        esp_rom_spiflash_write_encrypted = 0x40000114
                0x40000118                        esp_rom_spiflash_write_encrypted_dest = 0x40000118
                0x4000011c                        esp_rom_spiflash_write_encrypted_enable = 0x4000011c
                0x40000120                        esp_rom_spiflash_write_encrypted_disable = 0x40000120
                0x40000124                        esp_rom_spiflash_erase_chip = 0x40000124
                0x40000128                        _esp_rom_spiflash_erase_sector = 0x40000128
                0x4000012c                        _esp_rom_spiflash_erase_block = 0x4000012c
                0x40000130                        _esp_rom_spiflash_write = 0x40000130
                0x40000134                        _esp_rom_spiflash_read = 0x40000134
                0x40000138                        _esp_rom_spiflash_unlock = 0x40000138
                0x4000013c                        _SPIEraseArea = 0x4000013c
                0x40000140                        _SPI_write_enable = 0x40000140
                0x40000144                        esp_rom_spiflash_erase_sector = 0x40000144
                0x40000148                        esp_rom_spiflash_erase_block = 0x40000148
                0x4000014c                        esp_rom_spiflash_write = 0x4000014c
                0x40000150                        esp_rom_spiflash_read = 0x40000150
                0x40000154                        esp_rom_spiflash_unlock = 0x40000154
                0x40000158                        SPIEraseArea = 0x40000158
                0x4000015c                        SPI_write_enable = 0x4000015c
                0x40000160                        esp_rom_spiflash_config_param = 0x40000160
                0x40000164                        esp_rom_spiflash_read_user_cmd = 0x40000164
                0x40000168                        esp_rom_spiflash_select_qio_pins = 0x40000168
                0x4000016c                        esp_rom_spi_flash_auto_sus_res = 0x4000016c
                0x40000170                        esp_rom_spi_flash_send_resume = 0x40000170
                0x40000174                        esp_rom_spi_flash_update_id = 0x40000174
                0x40000178                        esp_rom_spiflash_config_clk = 0x40000178
                0x4000017c                        esp_rom_spiflash_config_readmode = 0x4000017c
                0x40000180                        esp_rom_spiflash_read_status = 0x40000180
                0x40000184                        esp_rom_spiflash_read_statushigh = 0x40000184
                0x40000188                        esp_rom_spiflash_write_status = 0x40000188
                0x4000018c                        spi_cache_mode_switch = 0x4000018c
                0x40000190                        spi_common_set_dummy_output = 0x40000190
                0x40000194                        spi_common_set_flash_cs_timing = 0x40000194
                0x40000198                        esp_rom_spi_set_address_bit_len = 0x40000198
                0x4000019c                        SPILock = 0x4000019c
                0x400001a0                        SPIMasterReadModeCnfig = 0x400001a0
                0x400001a4                        SPI_Common_Command = 0x400001a4
                0x400001a8                        SPI_WakeUp = 0x400001a8
                0x400001ac                        SPI_block_erase = 0x400001ac
                0x400001b0                        SPI_chip_erase = 0x400001b0
                0x400001b4                        SPI_init = 0x400001b4
                0x400001b8                        SPI_page_program = 0x400001b8
                0x400001bc                        SPI_read_data = 0x400001bc
                0x400001c0                        SPI_sector_erase = 0x400001c0
                0x400001c4                        SelectSpiFunction = 0x400001c4
                0x400001c8                        SetSpiDrvs = 0x400001c8
                0x400001cc                        Wait_SPI_Idle = 0x400001cc
                0x400001d0                        spi_dummy_len_fix = 0x400001d0
                0x400001d4                        Disable_QMode = 0x400001d4
                0x400001d8                        Enable_QMode = 0x400001d8
                0x400001dc                        spi_flash_attach = 0x400001dc
                0x400001e0                        spi_flash_get_chip_size = 0x400001e0
                0x400001e4                        spi_flash_guard_set = 0x400001e4
                0x400001e8                        spi_flash_guard_get = 0x400001e8
                0x400001ec                        spi_flash_read_encrypted = 0x400001ec
                0x4087fff0                        rom_spiflash_legacy_funcs = 0x4087fff0
                0x4087ffec                        rom_spiflash_legacy_data = 0x4087ffec
                0x4087fff4                        g_flash_guard_ops = 0x4087fff4
                0x40000278                        esp_rom_spiflash_write_disable = 0x40000278
                0x40000628                        Cache_Get_ICache_Line_Size = 0x40000628
                0x4000062c                        Cache_Get_Mode = 0x4000062c
                0x40000630                        Cache_Address_Through_Cache = 0x40000630
                0x40000634                        ROM_Boot_Cache_Init = 0x40000634
                0x40000638                        MMU_Set_Page_Mode = 0x40000638
                0x4000063c                        MMU_Get_Page_Mode = 0x4000063c
                0x40000640                        Cache_Invalidate_ICache_Items = 0x40000640
                0x40000644                        Cache_Op_Addr = 0x40000644
                0x40000648                        Cache_Invalidate_Addr = 0x40000648
                0x4000064c                        Cache_Invalidate_ICache_All = 0x4000064c
                0x40000650                        Cache_Mask_All = 0x40000650
                0x40000654                        Cache_UnMask_Dram0 = 0x40000654
                0x40000658                        Cache_Suspend_ICache_Autoload = 0x40000658
                0x4000065c                        Cache_Resume_ICache_Autoload = 0x4000065c
                0x40000660                        Cache_Start_ICache_Preload = 0x40000660
                0x40000664                        Cache_ICache_Preload_Done = 0x40000664
                0x40000668                        Cache_End_ICache_Preload = 0x40000668
                0x4000066c                        Cache_Config_ICache_Autoload = 0x4000066c
                0x40000670                        Cache_Enable_ICache_Autoload = 0x40000670
                0x40000674                        Cache_Disable_ICache_Autoload = 0x40000674
                0x40000678                        Cache_Enable_ICache_PreLock = 0x40000678
                0x4000067c                        Cache_Disable_ICache_PreLock = 0x4000067c
                0x40000680                        Cache_Lock_ICache_Items = 0x40000680
                0x40000684                        Cache_Unlock_ICache_Items = 0x40000684
                0x40000688                        Cache_Lock_Addr = 0x40000688
                0x4000068c                        Cache_Unlock_Addr = 0x4000068c
                0x40000690                        Cache_Disable_ICache = 0x40000690
                0x40000694                        Cache_Enable_ICache = 0x40000694
                0x40000698                        Cache_Suspend_ICache = 0x40000698
                0x4000069c                        Cache_Resume_ICache = 0x4000069c
                0x400006a0                        Cache_Freeze_ICache_Enable = 0x400006a0
                0x400006a4                        Cache_Freeze_ICache_Disable = 0x400006a4
                0x400006a8                        Cache_Set_IDROM_MMU_Size = 0x400006a8
                0x400006ac                        Cache_Get_IROM_MMU_End = 0x400006ac
                0x400006b0                        Cache_Get_DROM_MMU_End = 0x400006b0
                0x400006b4                        Cache_MMU_Init = 0x400006b4
                0x400006b8                        Cache_MSPI_MMU_Set = 0x400006b8
                0x400006bc                        Cache_Travel_Tag_Memory = 0x400006bc
                0x400006c0                        Cache_Get_Virtual_Addr = 0x400006c0
                0x4087ffcc                        rom_cache_op_cb = 0x4087ffcc
                0x4087ffc8                        rom_cache_internal_table_ptr = 0x4087ffc8
                0x400006c4                        ets_clk_get_xtal_freq = 0x400006c4
                0x400006c8                        ets_clk_get_cpu_freq = 0x400006c8
                0x400006cc                        ets_clk_apb_wait_ready = 0x400006cc
                0x400006d0                        ets_clk_mspi_apb_wait_ready = 0x400006d0
                0x400006d4                        gpio_input_get = 0x400006d4
                0x400006d8                        gpio_matrix_in = 0x400006d8
                0x400006dc                        gpio_matrix_out = 0x400006dc
                0x400006e0                        gpio_output_disable = 0x400006e0
                0x400006e4                        gpio_output_enable = 0x400006e4
                0x400006e8                        gpio_output_set = 0x400006e8
                0x400006ec                        gpio_pad_hold = 0x400006ec
                0x400006f0                        gpio_pad_input_disable = 0x400006f0
                0x400006f4                        gpio_pad_input_enable = 0x400006f4
                0x400006f8                        gpio_pad_pulldown = 0x400006f8
                0x400006fc                        gpio_pad_pullup = 0x400006fc
                0x40000700                        gpio_pad_select_gpio = 0x40000700
                0x40000704                        gpio_pad_set_drv = 0x40000704
                0x40000708                        gpio_pad_unhold = 0x40000708
                0x4000070c                        gpio_pin_wakeup_disable = 0x4000070c
                0x40000710                        gpio_pin_wakeup_enable = 0x40000710
                0x40000714                        gpio_bypass_matrix_in = 0x40000714
                0x40000718                        esprv_intc_int_set_priority = 0x40000718
                0x4000071c                        esprv_intc_int_set_threshold = 0x4000071c
                0x40000720                        esprv_intc_int_enable = 0x40000720
                0x40000724                        esprv_intc_int_disable = 0x40000724
                0x40000728                        esprv_intc_int_set_type = 0x40000728
                [!provide]                        PROVIDE (intr_handler_set = 0x4000072c)
                0x40000730                        intr_matrix_set = 0x40000730
                0x40000734                        ets_intr_lock = 0x40000734
                0x40000738                        ets_intr_unlock = 0x40000738
                0x4000073c                        ets_isr_attach = 0x4000073c
                0x40000740                        ets_isr_mask = 0x40000740
                0x40000744                        ets_isr_unmask = 0x40000744
                0x40000748                        md5_vector = 0x40000748
                0x4000074c                        MD5Init = 0x4000074c
                0x40000750                        MD5Update = 0x40000750
                0x40000754                        MD5Final = 0x40000754
                0x40000758                        crc32_le = 0x40000758
                0x4000075c                        crc16_le = 0x4000075c
                0x40000760                        crc8_le = 0x40000760
                0x40000764                        crc32_be = 0x40000764
                0x40000768                        crc16_be = 0x40000768
                0x4000076c                        crc8_be = 0x4000076c
                0x40000770                        esp_crc8 = 0x40000770
                0x40000774                        ets_sha_enable = 0x40000774
                0x40000778                        ets_sha_disable = 0x40000778
                0x4000077c                        ets_sha_get_state = 0x4000077c
                0x40000780                        ets_sha_init = 0x40000780
                0x40000784                        ets_sha_process = 0x40000784
                0x40000788                        ets_sha_starts = 0x40000788
                0x4000078c                        ets_sha_update = 0x4000078c
                0x40000790                        ets_sha_finish = 0x40000790
                0x40000794                        ets_sha_clone = 0x40000794
                0x40000798                        ets_hmac_enable = 0x40000798
                0x4000079c                        ets_hmac_disable = 0x4000079c
                0x400007a0                        ets_hmac_calculate_message = 0x400007a0
                0x400007a4                        ets_hmac_calculate_downstream = 0x400007a4
                0x400007a8                        ets_hmac_invalidate_downstream = 0x400007a8
                0x400007ac                        ets_jtag_enable_temporarily = 0x400007ac
                0x400007b0                        ets_aes_enable = 0x400007b0
                0x400007b4                        ets_aes_disable = 0x400007b4
                0x400007b8                        ets_aes_setkey = 0x400007b8
                0x400007bc                        ets_aes_block = 0x400007bc
                0x400007c0                        ets_aes_setkey_dec = 0x400007c0
                0x400007c4                        ets_aes_setkey_enc = 0x400007c4
                0x400007c8                        ets_bigint_enable = 0x400007c8
                0x400007cc                        ets_bigint_disable = 0x400007cc
                0x400007d0                        ets_bigint_multiply = 0x400007d0
                0x400007d4                        ets_bigint_modmult = 0x400007d4
                0x400007d8                        ets_bigint_modexp = 0x400007d8
                0x400007dc                        ets_bigint_wait_finish = 0x400007dc
                0x400007e0                        ets_bigint_getz = 0x400007e0
                0x400007e4                        ets_ds_enable = 0x400007e4
                0x400007e8                        ets_ds_disable = 0x400007e8
                0x400007ec                        ets_ds_start_sign = 0x400007ec
                0x400007f0                        ets_ds_is_busy = 0x400007f0
                0x400007f4                        ets_ds_finish_sign = 0x400007f4
                0x400007f8                        ets_ds_encrypt_params = 0x400007f8
                0x400007fc                        ets_mgf1_sha256 = 0x400007fc
                0x4004fff8                        crc32_le_table_ptr = 0x4004fff8
                0x4004fff4                        crc16_le_table_ptr = 0x4004fff4
                0x4004fff0                        crc8_le_table_ptr = 0x4004fff0
                0x4004ffec                        crc32_be_table_ptr = 0x4004ffec
                0x4004ffe8                        crc16_be_table_ptr = 0x4004ffe8
                0x4004ffe4                        crc8_be_table_ptr = 0x4004ffe4
                0x40000800                        ets_efuse_read = 0x40000800
                0x40000804                        ets_efuse_program = 0x40000804
                0x40000808                        ets_efuse_clear_program_registers = 0x40000808
                0x4000080c                        ets_efuse_write_key = 0x4000080c
                0x40000810                        ets_efuse_get_read_register_address = 0x40000810
                0x40000814                        ets_efuse_get_key_purpose = 0x40000814
                0x40000818                        ets_efuse_key_block_unused = 0x40000818
                0x4000081c                        ets_efuse_find_unused_key_block = 0x4000081c
                0x40000820                        ets_efuse_rs_calculate = 0x40000820
                0x40000824                        ets_efuse_count_unused_key_blocks = 0x40000824
                0x40000828                        ets_efuse_secure_boot_enabled = 0x40000828
                0x4000082c                        ets_efuse_secure_boot_aggressive_revoke_enabled = 0x4000082c
                0x40000830                        ets_efuse_cache_encryption_enabled = 0x40000830
                0x40000834                        ets_efuse_download_modes_disabled = 0x40000834
                0x40000838                        ets_efuse_find_purpose = 0x40000838
                0x4000083c                        ets_efuse_force_send_resume = 0x4000083c
                0x40000840                        ets_efuse_get_flash_delay_us = 0x40000840
                0x40000844                        ets_efuse_get_mac = 0x40000844
                0x40000848                        ets_efuse_get_uart_print_control = 0x40000848
                0x4000084c                        ets_efuse_direct_boot_mode_disabled = 0x4000084c
                0x40000850                        ets_efuse_security_download_modes_enabled = 0x40000850
                0x40000854                        ets_efuse_set_timing = 0x40000854
                0x40000858                        ets_efuse_jtag_disabled = 0x40000858
                0x4000085c                        ets_efuse_usb_print_is_disabled = 0x4000085c
                0x40000860                        ets_efuse_usb_download_mode_disabled = 0x40000860
                0x40000864                        ets_efuse_usb_device_disabled = 0x40000864
                0x40000868                        ets_efuse_secure_boot_fast_wake_enabled = 0x40000868
                0x4000086c                        ets_emsa_pss_verify = 0x4000086c
                0x40000870                        ets_rsa_pss_verify = 0x40000870
                0x40000874                        ets_secure_boot_verify_bootloader_with_keys = 0x40000874
                0x40000878                        ets_secure_boot_verify_signature = 0x40000878
                0x4000087c                        ets_secure_boot_read_key_digests = 0x4000087c
                0x40000880                        ets_secure_boot_revoke_public_key_digest = 0x40000880
                0x40000a80                        usb_serial_device_rx_one_char = 0x40000a80
                0x40000a84                        usb_serial_device_rx_one_char_block = 0x40000a84
                0x40000a88                        usb_serial_device_tx_flush = 0x40000a88
                0x40000a8c                        usb_serial_device_tx_one_char = 0x40000a8c
                0x40000a90                        lldesc_build_chain = 0x40000a90
                0x40000a94                        sip_after_tx_complete = 0x40000a94
                0x40000a98                        sip_alloc_to_host_evt = 0x40000a98
                0x40000a9c                        sip_download_begin = 0x40000a9c
                0x40000aa0                        sip_get_ptr = 0x40000aa0
                0x40000aa4                        sip_get_state = 0x40000aa4
                0x40000aa8                        sip_init_attach = 0x40000aa8
                0x40000aac                        sip_install_rx_ctrl_cb = 0x40000aac
                0x40000ab0                        sip_install_rx_data_cb = 0x40000ab0
                0x40000ab4                        sip_is_active = 0x40000ab4
                0x40000ab8                        sip_post_init = 0x40000ab8
                0x40000abc                        sip_reclaim_from_host_cmd = 0x40000abc
                0x40000ac0                        sip_reclaim_tx_data_pkt = 0x40000ac0
                0x40000ac4                        sip_send = 0x40000ac4
                0x40000ac8                        sip_to_host_chain_append = 0x40000ac8
                0x40000acc                        sip_to_host_evt_send_done = 0x40000acc
                0x40000ad0                        slc_add_credits = 0x40000ad0
                0x40000ad4                        slc_enable = 0x40000ad4
                0x40000ad8                        slc_from_host_chain_fetch = 0x40000ad8
                0x40000adc                        slc_from_host_chain_recycle = 0x40000adc
                0x40000ae0                        slc_has_pkt_to_host = 0x40000ae0
                0x40000ae4                        slc_init_attach = 0x40000ae4
                0x40000ae8                        slc_init_credit = 0x40000ae8
                0x40000aec                        slc_reattach = 0x40000aec
                0x40000af0                        slc_send_to_host_chain = 0x40000af0
                0x40000af4                        slc_set_host_io_max_window = 0x40000af4
                0x40000af8                        slc_to_host_chain_recycle = 0x40000af8
                0x40000758                        PROVIDE (esp_rom_crc32_le = crc32_le)
                [!provide]                        PROVIDE (esp_rom_crc16_le = crc16_le)
                [!provide]                        PROVIDE (esp_rom_crc8_le = crc8_le)
                [!provide]                        PROVIDE (esp_rom_crc32_be = crc32_be)
                [!provide]                        PROVIDE (esp_rom_crc16_be = crc16_be)
                [!provide]                        PROVIDE (esp_rom_crc8_be = crc8_be)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_select_gpio = gpio_pad_select_gpio)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_pullup_only = gpio_pad_pullup)
                0x40000704                        PROVIDE (esp_rom_gpio_pad_set_drv = gpio_pad_set_drv)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_unhold = gpio_pad_unhold)
                [!provide]                        PROVIDE (esp_rom_gpio_connect_in_signal = gpio_matrix_in)
                [!provide]                        PROVIDE (esp_rom_gpio_connect_out_signal = gpio_matrix_out)
                [!provide]                        PROVIDE (esp_rom_efuse_mac_address_crc8 = esp_crc8)
                [!provide]                        PROVIDE (esp_rom_efuse_is_secure_boot_enabled = ets_efuse_secure_boot_enabled)
                [!provide]                        PROVIDE (esp_rom_uart_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_uart_tx_one_char = uart_tx_one_char2)
                [!provide]                        PROVIDE (esp_rom_uart_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_uart_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_uart_rx_string = UartRxString)
                [!provide]                        PROVIDE (esp_rom_uart_set_as_console = uart_tx_switch)
                [!provide]                        PROVIDE (esp_rom_uart_putc = ets_write_char_uart)
                0x40000074                        PROVIDE (esp_rom_output_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_output_tx_one_char = uart_tx_one_char)
                0x40000078                        PROVIDE (esp_rom_output_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_output_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_output_rx_string = UartRxString)
                [!provide]                        PROVIDE (esp_rom_output_set_as_console = uart_tx_switch)
                [!provide]                        PROVIDE (esp_rom_output_putc = ets_write_char_uart)
                0x4000074c                        PROVIDE (esp_rom_md5_init = MD5Init)
                0x40000750                        PROVIDE (esp_rom_md5_update = MD5Update)
                0x40000754                        PROVIDE (esp_rom_md5_final = MD5Final)
                0x40000090                        PROVIDE (esp_rom_software_reset_system = software_reset)
                [!provide]                        PROVIDE (esp_rom_software_reset_cpu = software_reset_cpu)
                0x40000028                        PROVIDE (esp_rom_printf = ets_printf)
                0x40000034                        PROVIDE (esp_rom_install_uart_printf = ets_install_uart_printf)
                0x40000040                        PROVIDE (esp_rom_delay_us = ets_delay_us)
                0x40000018                        PROVIDE (esp_rom_get_reset_reason = rtc_get_reset_reason)
                [!provide]                        PROVIDE (esp_rom_route_intr_matrix = intr_matrix_set)
                0x40000044                        PROVIDE (esp_rom_get_cpu_ticks_per_us = ets_get_cpu_frequency)
                0x40000048                        PROVIDE (esp_rom_set_cpu_ticks_per_us = ets_update_cpu_frequency)
                [!provide]                        PROVIDE (esp_rom_spiflash_attach = spi_flash_attach)
                [!provide]                        PROVIDE (esp_rom_spiflash_clear_bp = esp_rom_spiflash_unlock)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_enable = SPI_write_enable)
                [!provide]                        PROVIDE (esp_rom_spiflash_erase_area = SPIEraseArea)
                0x400001d0                        PROVIDE (esp_rom_spiflash_fix_dummylen = spi_dummy_len_fix)
                [!provide]                        PROVIDE (esp_rom_spiflash_set_drvs = SetSpiDrvs)
                [!provide]                        PROVIDE (esp_rom_spiflash_select_padsfunc = SelectSpiFunction)
                [!provide]                        PROVIDE (esp_rom_spiflash_common_cmd = SPI_Common_Command)
                0x400009f4                        __adddf3 = 0x400009f4
                0x400009f8                        __addsf3 = 0x400009f8
                0x400009fc                        __eqdf2 = 0x400009fc
                0x40000a00                        __eqsf2 = 0x40000a00
                0x40000a04                        __extendsfdf2 = 0x40000a04
                0x40000a08                        __fixdfdi = 0x40000a08
                0x40000a0c                        __fixdfsi = 0x40000a0c
                0x40000a10                        __fixsfdi = 0x40000a10
                0x40000a14                        __fixsfsi = 0x40000a14
                0x40000a18                        __fixunsdfsi = 0x40000a18
                0x40000a1c                        __fixunssfdi = 0x40000a1c
                0x40000a20                        __fixunssfsi = 0x40000a20
                0x40000a24                        __floatdidf = 0x40000a24
                0x40000a28                        __floatdisf = 0x40000a28
                0x40000a2c                        __floatsidf = 0x40000a2c
                0x40000a30                        __floatsisf = 0x40000a30
                0x40000a34                        __floatundidf = 0x40000a34
                0x40000a38                        __floatundisf = 0x40000a38
                0x40000a3c                        __floatunsidf = 0x40000a3c
                0x40000a40                        __floatunsisf = 0x40000a40
                0x40000a44                        __gedf2 = 0x40000a44
                0x40000a48                        __gesf2 = 0x40000a48
                0x40000a4c                        __gtdf2 = 0x40000a4c
                0x40000a50                        __gtsf2 = 0x40000a50
                0x40000a54                        __ledf2 = 0x40000a54
                0x40000a58                        __lesf2 = 0x40000a58
                0x40000a5c                        __ltdf2 = 0x40000a5c
                0x40000a60                        __ltsf2 = 0x40000a60
                0x40000a64                        __muldf3 = 0x40000a64
                0x40000a68                        __mulsf3 = 0x40000a68
                0x40000a6c                        __nedf2 = 0x40000a6c
                0x40000a70                        __nesf2 = 0x40000a70
                0x40000a74                        __subdf3 = 0x40000a74
                0x40000a78                        __subsf3 = 0x40000a78
                0x40000a7c                        __truncdfsf2 = 0x40000a7c
                0x40000884                        __absvdi2 = 0x40000884
                0x40000888                        __absvsi2 = 0x40000888
                0x40000894                        __addvdi3 = 0x40000894
                0x40000898                        __addvsi3 = 0x40000898
                0x4000089c                        __ashldi3 = 0x4000089c
                0x400008a0                        __ashrdi3 = 0x400008a0
                0x400008a4                        __bswapdi2 = 0x400008a4
                0x400008a8                        __bswapsi2 = 0x400008a8
                0x400008ac                        __clear_cache = 0x400008ac
                0x400008b0                        __clrsbdi2 = 0x400008b0
                0x400008b4                        __clrsbsi2 = 0x400008b4
                0x400008b8                        __clzdi2 = 0x400008b8
                0x400008bc                        __clzsi2 = 0x400008bc
                0x400008c0                        __cmpdi2 = 0x400008c0
                0x400008c4                        __ctzdi2 = 0x400008c4
                0x400008c8                        __ctzsi2 = 0x400008c8
                0x400008cc                        __divdc3 = 0x400008cc
                0x400008d0                        __divdf3 = 0x400008d0
                0x400008d4                        __divdi3 = 0x400008d4
                0x400008d8                        __divsc3 = 0x400008d8
                0x400008dc                        __divsf3 = 0x400008dc
                0x400008e0                        __divsi3 = 0x400008e0
                0x400008f0                        __ffsdi2 = 0x400008f0
                0x400008f4                        __ffssi2 = 0x400008f4
                0x40000934                        __gcc_bcmp = 0x40000934
                0x40000950                        __lshrdi3 = 0x40000950
                0x4000095c                        __moddi3 = 0x4000095c
                0x40000960                        __modsi3 = 0x40000960
                0x40000964                        __muldc3 = 0x40000964
                0x4000096c                        __muldi3 = 0x4000096c
                0x40000970                        __mulsc3 = 0x40000970
                0x40000978                        __mulsi3 = 0x40000978
                0x4000097c                        __mulvdi3 = 0x4000097c
                0x40000980                        __mulvsi3 = 0x40000980
                0x40000988                        __negdf2 = 0x40000988
                0x4000098c                        __negdi2 = 0x4000098c
                0x40000990                        __negsf2 = 0x40000990
                0x40000994                        __negvdi2 = 0x40000994
                0x40000998                        __negvsi2 = 0x40000998
                0x400009a0                        __paritysi2 = 0x400009a0
                0x400009a4                        __popcountdi2 = 0x400009a4
                0x400009a8                        __popcountsi2 = 0x400009a8
                0x400009ac                        __powidf2 = 0x400009ac
                0x400009b0                        __powisf2 = 0x400009b0
                0x400009bc                        __subvdi3 = 0x400009bc
                0x400009c0                        __subvsi3 = 0x400009c0
                0x400009c8                        __ucmpdi2 = 0x400009c8
                0x400009cc                        __udivdi3 = 0x400009cc
                0x400009d0                        __udivmoddi4 = 0x400009d0
                0x400009d4                        __udivsi3 = 0x400009d4
                0x400009d8                        __udiv_w_sdiv = 0x400009d8
                0x400009dc                        __umoddi3 = 0x400009dc
                0x400009e0                        __umodsi3 = 0x400009e0
                0x400009e4                        __unorddf2 = 0x400009e4
                0x400009e8                        __unordsf2 = 0x400009e8
                0x400009ec                        __extenddftf2 = 0x400009ec
                0x400009f0                        __trunctfdf2 = 0x400009f0
                0x4000039c                        wdt_hal_config_stage = 0x4000039c
                0x400003a0                        wdt_hal_write_protect_disable = 0x400003a0
                0x400003a4                        wdt_hal_write_protect_enable = 0x400003a4
                0x400003a8                        wdt_hal_enable = 0x400003a8
                0x400003ac                        wdt_hal_disable = 0x400003ac
                0x400003b0                        wdt_hal_handle_intr = 0x400003b0
                0x400003b4                        wdt_hal_feed = 0x400003b4
                0x400003b8                        wdt_hal_set_flashboot_en = 0x400003b8
                0x400003bc                        wdt_hal_is_enabled = 0x400003bc
                0x400003c8                        systimer_hal_set_tick_rate_ops = 0x400003c8
                0x400003cc                        systimer_hal_get_counter_value = 0x400003cc
                0x400003d0                        systimer_hal_get_time = 0x400003d0
                0x400003d4                        systimer_hal_set_alarm_target = 0x400003d4
                0x400003d8                        systimer_hal_set_alarm_period = 0x400003d8
                0x400003dc                        systimer_hal_get_alarm_value = 0x400003dc
                0x400003e0                        systimer_hal_enable_alarm_int = 0x400003e0
                0x400003e4                        systimer_hal_on_apb_freq_update = 0x400003e4
                0x400003e8                        systimer_hal_counter_value_advance = 0x400003e8
                0x400003ec                        systimer_hal_enable_counter = 0x400003ec
                0x400003f0                        systimer_hal_select_alarm_mode = 0x400003f0
                0x400003f4                        systimer_hal_connect_alarm_counter = 0x400003f4
                0x400003f8                        systimer_hal_counter_can_stall_by_cpu = 0x400003f8
                0x40000010                        _rom_chip_id = 0x40000010
                0x40000014                        _rom_eco_version = 0x40000014
                0x40001104                        phy_param_addr = 0x40001104
                0x40001108                        phy_get_romfuncs = 0x40001108
                0x4000110c                        chip761_phyrom_version = 0x4000110c
                0x40001110                        chip761_phyrom_version_num = 0x40001110
                0x40001114                        get_rc_dout = 0x40001114
                0x40001118                        rc_cal = 0x40001118
                0x4000111c                        rom_enter_critical_phy = 0x4000111c
                0x40001120                        rom_exit_critical_phy = 0x40001120
                0x40001124                        rom_set_chan_cal_interp = 0x40001124
                0x40001128                        rom_loopback_mode_en = 0x40001128
                0x4000112c                        rom_bb_bss_cbw40 = 0x4000112c
                0x40001130                        abs_temp = 0x40001130
                0x40001134                        get_data_sat = 0x40001134
                0x40001138                        phy_byte_to_word = 0x40001138
                0x4000113c                        set_chan_reg = 0x4000113c
                0x40001140                        i2c_master_reset = 0x40001140
                0x40001144                        rom_set_chan_freq_sw_start = 0x40001144
                0x40001148                        freq_module_resetn = 0x40001148
                0x4000114c                        freq_chan_en_sw = 0x4000114c
                0x40001150                        write_chan_freq = 0x40001150
                0x40001154                        get_freq_mem_param = 0x40001154
                0x40001158                        get_freq_mem_addr = 0x40001158
                0x4000115c                        bt_txpwr_freq = 0x4000115c
                0x40001160                        wr_rf_freq_mem = 0x40001160
                0x40001164                        read_rf_freq_mem = 0x40001164
                0x40001168                        freq_i2c_mem_write = 0x40001168
                0x4000116c                        freq_num_get_data = 0x4000116c
                0x40001170                        freq_i2c_num_addr = 0x40001170
                0x40001174                        freq_i2c_write_set = 0x40001174
                0x40001178                        pll_dac_mem_update = 0x40001178
                0x4000117c                        pll_cap_mem_update = 0x4000117c
                0x40001180                        get_rf_freq_cap = 0x40001180
                0x40001184                        get_rf_freq_init = 0x40001184
                0x40001188                        phy_en_hw_set_freq = 0x40001188
                0x4000118c                        phy_dis_hw_set_freq = 0x4000118c
                0x40001190                        rom_pwdet_sar2_init = 0x40001190
                0x40001194                        rom_en_pwdet = 0x40001194
                0x40001198                        rom_get_sar_sig_ref = 0x40001198
                0x4000119c                        rom_pwdet_tone_start = 0x4000119c
                0x400011a0                        rom_pwdet_wait_idle = 0x400011a0
                0x400011a4                        rom_read_sar_dout = 0x400011a4
                0x400011a8                        get_tone_sar_dout = 0x400011a8
                0x400011ac                        get_fm_sar_dout = 0x400011ac
                0x400011b0                        txtone_linear_pwr = 0x400011b0
                0x400011b4                        linear_to_db = 0x400011b4
                0x400011b8                        get_power_db = 0x400011b8
                0x400011bc                        meas_tone_pwr_db = 0x400011bc
                0x400011c0                        pkdet_vol_start = 0x400011c0
                0x400011c4                        read_sar2_code = 0x400011c4
                0x400011c8                        get_sar2_vol = 0x400011c8
                0x400011cc                        get_pll_vol = 0x400011cc
                0x400011d0                        tx_pwctrl_bg_init = 0x400011d0
                0x400011d4                        phy_pwdet_always_en = 0x400011d4
                0x400011d8                        phy_pwdet_onetime_en = 0x400011d8
                0x400011dc                        esp_tx_state_out_rom = 0x400011dc
                0x400011e0                        ant_dft_cfg_rom = 0x400011e0
                0x400011e4                        ant_wifitx_cfg_rom = 0x400011e4
                0x400011e8                        ant_wifirx_cfg_rom = 0x400011e8
                0x400011ec                        ant_bttx_cfg_rom = 0x400011ec
                0x400011f0                        ant_btrx_cfg_rom = 0x400011f0
                0x400011f4                        phy_chan_dump_cfg_rom = 0x400011f4
                0x400011f8                        phy_enable_low_rate = 0x400011f8
                0x400011fc                        phy_disable_low_rate = 0x400011fc
                0x40001200                        phy_is_low_rate_enabled = 0x40001200
                0x40001204                        phy_dig_reg_backup_rom = 0x40001204
                0x40001208                        phy_chan_filt_set_rom = 0x40001208
                0x4000120c                        phy_rx11blr_cfg = 0x4000120c
                0x40001210                        set_cca_rom = 0x40001210
                0x40001214                        set_rx_sense_rom = 0x40001214
                0x40001218                        rx_gain_force_rom = 0x40001218
                0x4000121c                        rom_rfpll_set_freq = 0x4000121c
                0x40001220                        mhz2ieee = 0x40001220
                0x40001224                        chan_to_freq = 0x40001224
                0x40001228                        restart_cal = 0x40001228
                0x4000122c                        write_rfpll_sdm = 0x4000122c
                0x40001230                        wait_rfpll_cal_end = 0x40001230
                0x40001234                        set_rf_freq_offset = 0x40001234
                0x40001238                        set_rfpll_freq = 0x40001238
                0x4000123c                        set_channel_rfpll_freq = 0x4000123c
                0x40001240                        rfpll_cap_correct = 0x40001240
                0x40001244                        rfpll_cap_init_cal = 0x40001244
                0x40001248                        write_pll_cap = 0x40001248
                0x4000124c                        read_pll_cap = 0x4000124c
                0x40001250                        chip_v7_set_chan_ana = 0x40001250
                0x40001254                        freq_set_reg = 0x40001254
                0x40001258                        gen_rx_gain_table = 0x40001258
                0x4000125c                        bt_txdc_cal = 0x4000125c
                0x40001260                        bt_txiq_cal = 0x40001260
                0x40001264                        txiq_cal_init = 0x40001264
                0x40001268                        txdc_cal_init = 0x40001268
                0x4000126c                        txdc_cal = 0x4000126c
                0x40001270                        txiq_get_mis_pwr = 0x40001270
                0x40001274                        txiq_cover = 0x40001274
                0x40001278                        rfcal_txiq = 0x40001278
                0x4000127c                        get_power_atten = 0x4000127c
                0x40001280                        pwdet_ref_code = 0x40001280
                0x40001284                        pwdet_code_cal = 0x40001284
                0x40001288                        rfcal_txcap = 0x40001288
                0x4000128c                        tx_cap_init = 0x4000128c
                0x40001290                        rfcal_pwrctrl = 0x40001290
                0x40001294                        tx_pwctrl_init_cal = 0x40001294
                0x40001298                        tx_pwctrl_init = 0x40001298
                0x4000129c                        bt_tx_pwctrl_init = 0x4000129c
                0x400012a0                        rom_i2c_enter_critical = 0x400012a0
                0x400012a4                        rom_i2c_exit_critical = 0x400012a4
                0x400012a8                        rom_get_i2c_read_mask = 0x400012a8
                0x400012ac                        rom_get_i2c_mst0_mask = 0x400012ac
                0x400012b0                        rom_get_i2c_hostid = 0x400012b0
                0x400012b4                        rom_chip_i2c_readReg_org = 0x400012b4
                0x400012b8                        rom_chip_i2c_readReg = 0x400012b8
                0x400012c0                        rom_chip_i2c_writeReg = 0x400012c0
                0x400012d0                        rom_set_txcap_reg = 0x400012d0
                0x400012d4                        i2c_paral_set_mst0 = 0x400012d4
                0x400012d8                        i2c_paral_set_read = 0x400012d8
                0x400012dc                        i2c_paral_read = 0x400012dc
                0x400012e0                        i2c_paral_write = 0x400012e0
                0x400012e4                        i2c_paral_write_num = 0x400012e4
                0x400012e8                        i2c_paral_write_mask = 0x400012e8
                0x400012ec                        i2c_sar2_init_code = 0x400012ec
                0x400012f0                        rom_pbus_force_mode = 0x400012f0
                0x400012f4                        rom_pbus_rd_addr = 0x400012f4
                0x400012f8                        rom_pbus_rd_shift = 0x400012f8
                0x400012fc                        rom_pbus_force_test = 0x400012fc
                0x40001300                        rom_pbus_rd = 0x40001300
                0x40001304                        rom_pbus_set_rxgain = 0x40001304
                0x40001308                        rom_pbus_xpd_rx_off = 0x40001308
                0x4000130c                        rom_pbus_xpd_rx_on = 0x4000130c
                0x40001310                        rom_pbus_xpd_tx_off = 0x40001310
                0x40001314                        rom_pbus_xpd_tx_on = 0x40001314
                0x40001318                        rom_set_loopback_gain = 0x40001318
                0x4000131c                        rom_txcal_debuge_mode = 0x4000131c
                0x40001320                        pbus_debugmode = 0x40001320
                0x40001324                        pbus_workmode = 0x40001324
                0x40001328                        pbus_set_dco = 0x40001328
                0x4000132c                        txcal_work_mode = 0x4000132c
                0x40001330                        rom_start_tx_tone_step = 0x40001330
                0x40001334                        rom_stop_tx_tone = 0x40001334
                0x40001338                        disable_agc = 0x40001338
                0x4000133c                        enable_agc = 0x4000133c
                0x40001340                        phy_disable_cca = 0x40001340
                0x40001344                        phy_enable_cca = 0x40001344
                0x40001348                        write_gain_mem = 0x40001348
                0x4000134c                        bb_bss_cbw40_dig = 0x4000134c
                0x40001350                        cbw2040_cfg = 0x40001350
                0x40001354                        mac_tx_chan_offset = 0x40001354
                0x40001358                        tx_paon_set = 0x40001358
                0x4000135c                        pwdet_reg_init = 0x4000135c
                0x40001360                        i2cmst_reg_init = 0x40001360
                0x40001364                        bt_gain_offset = 0x40001364
                0x40001368                        fe_reg_init = 0x40001368
                0x4000136c                        mac_enable_bb = 0x4000136c
                0x40001370                        bb_wdg_cfg = 0x40001370
                0x40001374                        fe_txrx_reset = 0x40001374
                0x40001378                        set_rx_comp = 0x40001378
                0x4000137c                        agc_reg_init = 0x4000137c
                0x40001380                        bb_reg_init = 0x40001380
                0x40001384                        open_i2c_xpd = 0x40001384
                0x40001388                        txiq_set_reg = 0x40001388
                0x4000138c                        rxiq_set_reg = 0x4000138c
                0x40001390                        set_txclk_en = 0x40001390
                0x40001394                        set_rxclk_en = 0x40001394
                0x40001398                        bb_wdg_test_en = 0x40001398
                0x4000139c                        noise_floor_auto_set = 0x4000139c
                0x400013a0                        read_hw_noisefloor = 0x400013a0
                0x400013a4                        iq_corr_enable = 0x400013a4
                0x400013a8                        wifi_agc_sat_gain = 0x400013a8
                0x400013ac                        phy_bbpll_cal = 0x400013ac
                0x400013b0                        phy_ant_init = 0x400013b0
                0x400013b4                        phy_set_bbfreq_init = 0x400013b4
                0x400013b8                        wifi_fbw_sel = 0x400013b8
                0x400013bc                        bt_filter_reg = 0x400013bc
                0x400013c0                        phy_rx_sense_set = 0x400013c0
                0x400013c4                        tx_state_set = 0x400013c4
                0x400013c8                        phy_close_pa = 0x400013c8
                0x400013cc                        phy_freq_correct = 0x400013cc
                0x400013d0                        set_pbus_reg = 0x400013d0
                0x400013d4                        wifi_rifs_mode_en = 0x400013d4
                0x400013d8                        nrx_freq_set = 0x400013d8
                0x400013dc                        fe_adc_on = 0x400013dc
                0x400013e0                        phy_force_pwr_index = 0x400013e0
                0x400013e4                        rom_iq_est_enable = 0x400013e4
                0x400013e8                        rom_iq_est_disable = 0x400013e8
                0x400013ec                        rom_bb_gain_index = 0x400013ec
                0x400013f0                        rom_rfrx_gain_index = 0x400013f0
                0x400013f4                        dc_iq_est = 0x400013f4
                0x400013f8                        set_cal_rxdc = 0x400013f8
                0x400013fc                        rxiq_get_mis = 0x400013fc
                0x40001400                        rxiq_cover_mg_mp = 0x40001400
                0x40001404                        rfcal_rxiq = 0x40001404
                0x40001408                        get_rfcal_rxiq_data = 0x40001408
                0x4000140c                        get_dco_comp = 0x4000140c
                0x40001410                        pbus_rx_dco_cal = 0x40001410
                0x40001414                        rxdc_est_min = 0x40001414
                0x40001418                        pbus_rx_dco_cal_1step = 0x40001418
                0x4000141c                        set_lb_txiq = 0x4000141c
                0x40001420                        set_rx_gain_cal_iq = 0x40001420
                0x40001424                        set_rx_gain_cal_dc = 0x40001424
                0x40001428                        spur_reg_write_one_tone = 0x40001428
                0x4000142c                        spur_cal = 0x4000142c
                0x40001430                        spur_coef_cfg = 0x40001430
                0x40001434                        tsens_power_up = 0x40001434
                0x40001438                        tsens_read_init = 0x40001438
                0x4000143c                        code_to_temp = 0x4000143c
                0x40001440                        tsens_index_to_dac = 0x40001440
                0x40001444                        tsens_index_to_offset = 0x40001444
                0x40001448                        tsens_dac_cal = 0x40001448
                0x4000144c                        tsens_code_read = 0x4000144c
                0x40001450                        tsens_temp_read = 0x40001450
                0x40001454                        temp_to_power = 0x40001454
                0x40001458                        get_temp_init = 0x40001458
                0x4000145c                        txbbgain_to_index = 0x4000145c
                0x40001460                        index_to_txbbgain = 0x40001460
                0x40001464                        bt_index_to_bb = 0x40001464
                0x40001468                        bt_bb_to_index = 0x40001468
                0x4000146c                        bt_get_tx_gain = 0x4000146c
                0x40001470                        dig_gain_check = 0x40001470
                0x40001474                        wifi_get_tx_gain = 0x40001474
                0x40001478                        wifi_11g_rate_chg = 0x40001478
                0x4000147c                        bt_chan_pwr_interp = 0x4000147c
                0x40001480                        get_rate_fcc_index = 0x40001480
                0x40001484                        get_chan_target_power = 0x40001484
                0x40001488                        get_tx_gain_value = 0x40001488
                0x4000148c                        wifi_get_target_power = 0x4000148c
                0x4087fce8                        phy_param_rom = 0x4087fce8
                0x400004a4                        esp_rom_newlib_init_common_mutexes = 0x400004a4
                0x400004a8                        memset = 0x400004a8
                0x400004ac                        memcpy = 0x400004ac
                0x400004b0                        memmove = 0x400004b0
                0x400004b4                        memcmp = 0x400004b4
                0x400004b8                        strcpy = 0x400004b8
                0x400004bc                        strncpy = 0x400004bc
                0x400004c0                        strcmp = 0x400004c0
                0x400004c4                        strncmp = 0x400004c4
                0x400004c8                        strlen = 0x400004c8
                0x400004cc                        strstr = 0x400004cc
                0x400004d0                        bzero = 0x400004d0
                0x400004d4                        _isatty_r = 0x400004d4
                0x400004d8                        sbrk = 0x400004d8
                0x400004dc                        isalnum = 0x400004dc
                0x400004e0                        isalpha = 0x400004e0
                0x400004e4                        isascii = 0x400004e4
                0x400004e8                        isblank = 0x400004e8
                0x400004ec                        iscntrl = 0x400004ec
                0x400004f0                        isdigit = 0x400004f0
                0x400004f4                        islower = 0x400004f4
                0x400004f8                        isgraph = 0x400004f8
                0x400004fc                        isprint = 0x400004fc
                0x40000500                        ispunct = 0x40000500
                0x40000504                        isspace = 0x40000504
                0x40000508                        isupper = 0x40000508
                0x4000050c                        toupper = 0x4000050c
                0x40000510                        tolower = 0x40000510
                0x40000514                        toascii = 0x40000514
                0x40000518                        memccpy = 0x40000518
                0x4000051c                        memchr = 0x4000051c
                0x40000520                        memrchr = 0x40000520
                0x40000524                        strcasecmp = 0x40000524
                0x40000528                        strcasestr = 0x40000528
                0x4000052c                        strcat = 0x4000052c
                0x40000530                        strdup = 0x40000530
                0x40000534                        strchr = 0x40000534
                0x40000538                        strcspn = 0x40000538
                0x4000053c                        strcoll = 0x4000053c
                0x40000540                        strlcat = 0x40000540
                0x40000544                        strlcpy = 0x40000544
                0x40000548                        strlwr = 0x40000548
                0x4000054c                        strncasecmp = 0x4000054c
                0x40000550                        strncat = 0x40000550
                0x40000554                        strndup = 0x40000554
                0x40000558                        strnlen = 0x40000558
                0x4000055c                        strrchr = 0x4000055c
                0x40000560                        strsep = 0x40000560
                0x40000564                        strspn = 0x40000564
                0x40000568                        strtok_r = 0x40000568
                0x4000056c                        strupr = 0x4000056c
                0x40000570                        longjmp = 0x40000570
                0x40000574                        setjmp = 0x40000574
                0x40000578                        abs = 0x40000578
                0x4000057c                        div = 0x4000057c
                0x40000580                        labs = 0x40000580
                0x40000584                        ldiv = 0x40000584
                0x40000588                        qsort = 0x40000588
                0x4000058c                        rand_r = 0x4000058c
                0x40000590                        rand = 0x40000590
                0x40000594                        srand = 0x40000594
                0x40000598                        utoa = 0x40000598
                0x4000059c                        itoa = 0x4000059c
                0x400005a0                        atoi = 0x400005a0
                0x400005a4                        atol = 0x400005a4
                0x400005a8                        strtol = 0x400005a8
                0x400005ac                        strtoul = 0x400005ac
                0x400005b0                        fflush = 0x400005b0
                0x400005b4                        _fflush_r = 0x400005b4
                0x400005b8                        _fwalk = 0x400005b8
                0x400005bc                        _fwalk_reent = 0x400005bc
                0x400005c0                        __smakebuf_r = 0x400005c0
                0x400005c4                        __swhatbuf_r = 0x400005c4
                0x400005c8                        __swbuf_r = 0x400005c8
                0x400005cc                        __swbuf = 0x400005cc
                0x400005d0                        __swsetup_r = 0x400005d0
                0x4087ffd4                        syscall_table_ptr = 0x4087ffd4
                0x4087ffd0                        _global_impure_ptr = 0x4087ffd0
                0x60000000                        PROVIDE (UART0 = 0x60000000)
                [!provide]                        PROVIDE (UART1 = 0x60001000)
                0x60002000                        PROVIDE (SPIMEM0 = 0x60002000)
                0x60003000                        PROVIDE (SPIMEM1 = 0x60003000)
                [!provide]                        PROVIDE (I2C0 = 0x60004000)
                [!provide]                        PROVIDE (UHCI0 = 0x60005000)
                [!provide]                        PROVIDE (RMT = 0x60006000)
                [!provide]                        PROVIDE (RMTMEM = 0x60006400)
                [!provide]                        PROVIDE (LEDC = 0x60007000)
                0x60008000                        PROVIDE (TIMERG0 = 0x60008000)
                0x60009000                        PROVIDE (TIMERG1 = 0x60009000)
                [!provide]                        PROVIDE (SYSTIMER = 0x6000a000)
                [!provide]                        PROVIDE (TWAI0 = 0x6000b000)
                [!provide]                        PROVIDE (I2S0 = 0x6000c000)
                [!provide]                        PROVIDE (TWAI1 = 0x6000d000)
                [!provide]                        PROVIDE (APB_SARADC = 0x6000e000)
                [!provide]                        PROVIDE (USB_SERIAL_JTAG = 0x6000f000)
                [!provide]                        PROVIDE (INTMTX = 0x60010000)
                [!provide]                        PROVIDE (ATOMIC_LOCKER = 0x60011000)
                [!provide]                        PROVIDE (PCNT = 0x60012000)
                [!provide]                        PROVIDE (SOC_ETM = 0x60013000)
                [!provide]                        PROVIDE (MCPWM0 = 0x60014000)
                [!provide]                        PROVIDE (PARL_IO = 0x60015000)
                [!provide]                        PROVIDE (HINF = 0x60016000)
                [!provide]                        PROVIDE (SLC = 0x60017000)
                [!provide]                        PROVIDE (HOST = 0x60018000)
                [!provide]                        PROVIDE (PVT_MONITOR = 0x60019000)
                [!provide]                        PROVIDE (GDMA = 0x60080000)
                [!provide]                        PROVIDE (GPSPI2 = 0x60081000)
                [!provide]                        PROVIDE (AES = 0x60088000)
                [!provide]                        PROVIDE (SHA = 0x60089000)
                [!provide]                        PROVIDE (RSA = 0x6008a000)
                [!provide]                        PROVIDE (ECC = 0x6008b000)
                [!provide]                        PROVIDE (DS = 0x6008c000)
                [!provide]                        PROVIDE (HMAC = 0x6008d000)
                [!provide]                        PROVIDE (IO_MUX = 0x60090000)
                [!provide]                        PROVIDE (GPIO = 0x60091000)
                [!provide]                        PROVIDE (GPIO_EXT = 0x60091f00)
                [!provide]                        PROVIDE (SDM = 0x60091f00)
                [!provide]                        PROVIDE (GLITCH_FILTER = 0x60091f30)
                [!provide]                        PROVIDE (GPIO_ETM = 0x60091f60)
                [!provide]                        PROVIDE (MEM_MONITOR = 0x60092000)
                [!provide]                        PROVIDE (PAU = 0x60093000)
                [!provide]                        PROVIDE (HP_SYSTEM = 0x60095000)
                0x60096000                        PROVIDE (PCR = 0x60096000)
                [!provide]                        PROVIDE (TEE = 0x60098000)
                [!provide]                        PROVIDE (HP_APM = 0x60099000)
                [!provide]                        PROVIDE (IEEE802154 = 0x600a3000)
                0x600a9800                        PROVIDE (MODEM_SYSCON = 0x600a9800)
                0x600af000                        PROVIDE (MODEM_LPCON = 0x600af000)
                0x600b0000                        PROVIDE (PMU = 0x600b0000)
                0x600b0400                        PROVIDE (LP_CLKRST = 0x600b0400)
                0x600b0800                        PROVIDE (EFUSE = 0x600b0800)
                0x600b0c00                        PROVIDE (LP_TIMER = 0x600b0c00)
                [!provide]                        PROVIDE (LP_AON = 0x600b1000)
                0x600b1400                        PROVIDE (LP_UART = 0x600b1400)
                [!provide]                        PROVIDE (LP_I2C = 0x600b1800)
                0x600b1c00                        PROVIDE (LP_WDT = 0x600b1c00)
                [!provide]                        PROVIDE (LP_IO = 0x600b2000)
                [!provide]                        PROVIDE (LP_I2C_ANA_MST = 0x600b2400)
                [!provide]                        PROVIDE (LPPERI = 0x600b2800)
                [!provide]                        PROVIDE (LP_ANA_PERI = 0x600b2c00)
                [!provide]                        PROVIDE (LP_APM = 0x600b3800)
                [!provide]                        PROVIDE (OTP_DEBUG = 0x600b3c00)
                0x4087c610                        bootloader_usable_dram_end = 0x4087c610
                0x00002000                        bootloader_stack_overhead = 0x2000
                0x00005000                        bootloader_dram_seg_len = 0x5000
                0x00007000                        bootloader_iram_loader_seg_len = 0x7000
                0x00002500                        bootloader_iram_seg_len = 0x2500
                0x4087a610                        bootloader_dram_seg_end = (bootloader_usable_dram_end - bootloader_stack_overhead)
                0x40875610                        bootloader_dram_seg_start = (bootloader_dram_seg_end - bootloader_dram_seg_len)
                0x4086e610                        bootloader_iram_loader_seg_start = (bootloader_dram_seg_start - bootloader_iram_loader_seg_len)
                0x4086c110                        bootloader_iram_seg_start = (bootloader_iram_loader_seg_start - bootloader_iram_seg_len)
                0x00000001                        ASSERT ((bootloader_iram_loader_seg_start == 0x4086e610), bootloader_iram_loader_seg_start inconsistent with SRAM_DRAM_END)

.iram_loader.text
                0x4086e610     0x2e58
                0x4086e610                        . = ALIGN (0x10)
                0x4086e610                        _loader_text_start = ABSOLUTE (.)
 *(.stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 *(.iram1 .iram1.*)
 .iram1.0       0x4086e610        0x4 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                0x4086e610                esp_flash_encryption_enabled
 .iram1.5       0x4086e614       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.1       0x4086e664      0x1b0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086e664                bootloader_flash_execute_command_common
 .iram1.2       0x4086e814        0xe esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086e814                bootloader_execute_flash_command
 .iram1.0       0x4086e822      0x15a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086e822                bootloader_flash_unlock
 .iram1.3       0x4086e97c       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086e97c                bootloader_flash_read_sfdp
 .iram1.4       0x4086e9bc       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086e9bc                bootloader_read_flash_id
 .iram1.6       0x4086e9f0       0xbe esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086e9f0                bootloader_flash_xmc_startup
 .iram1.0       0x4086eaae       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                0x4086eaae                bootloader_flash_cs_timing_config
 .iram1.2       0x4086eace       0x56 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                0x4086eace                bootloader_configure_spi_pins
 .iram1.0       0x4086eb24       0xf2 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .iram1.3       0x4086ec16       0x50 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
                0x4086ec16                esp_rom_regi2c_write
                0x4086ec16                regi2c_write_impl
 .iram1.4       0x4086ec66       0xdc esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
                0x4086ec66                regi2c_write_mask_impl
                0x4086ec66                esp_rom_regi2c_write_mask
 .iram1.0       0x4086ed42       0x1e esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4086ed42                efuse_hal_chip_revision
 .iram1.1       0x4086ed60       0x1e esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4086ed60                efuse_hal_blk_version
 .iram1.2       0x4086ed7e        0xe esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4086ed7e                efuse_hal_get_disable_wafer_version_major
 .iram1.3       0x4086ed8c       0x22 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4086ed8c                efuse_hal_flash_encryption_enabled
 .iram1.0       0x4086edae       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4086edae                efuse_hal_get_major_chip_version
 .iram1.1       0x4086edbe       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4086edbe                efuse_hal_get_minor_chip_version
 .iram1.2       0x4086edce       0x28 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
                0x4086edce                lp_timer_hal_get_cycle_count
 *liblog.a:(.literal .text .literal.* .text.*)
 .text.esp_log_early_timestamp
                0x4086edf6       0x26 esp-idf/log/liblog.a(log_noos.c.obj)
                0x4086edf6                esp_log_early_timestamp
                0x4086edf6                esp_log_timestamp
 *libgcc.a:(.literal .text .literal.* .text.*)
 *libclang_rt.builtins.a:(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_clock_loader.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_common_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_common_ota_select_crc
                0x4086ee1c        0xe esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4086ee1c                bootloader_common_ota_select_crc
 .text.bootloader_common_ota_select_invalid
                0x4086ee2a       0x16 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4086ee2a                bootloader_common_ota_select_invalid
 .text.bootloader_common_ota_select_valid
                0x4086ee40       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4086ee40                bootloader_common_ota_select_valid
 .text.bootloader_common_check_chip_validity
                0x4086ee6a      0x118 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4086ee6a                bootloader_common_check_chip_validity
 .text.bootloader_common_select_otadata
                0x4086ef82       0x3a esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4086ef82                bootloader_common_select_otadata
 .text.bootloader_common_get_active_otadata
                0x4086efbc       0x2e esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4086efbc                bootloader_common_get_active_otadata
 *libbootloader_support.a:bootloader_flash.*(.literal .text .literal.* .text.*)
 .text.spi_to_esp_err
                0x4086efea       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_mmap_get_free_pages
                0x4086f00c        0x6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086f00c                bootloader_mmap_get_free_pages
 .text.bootloader_mmap
                0x4086f012       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086f012                bootloader_mmap
 .text.bootloader_munmap
                0x4086f0da       0x32 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086f0da                bootloader_munmap
 .text.bootloader_flash_read
                0x4086f10c      0x138 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086f10c                bootloader_flash_read
 .text.bootloader_flash_erase_sector
                0x4086f244       0x12 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086f244                bootloader_flash_erase_sector
 .text.bootloader_flash_write
                0x4086f256       0xca esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086f256                bootloader_flash_write
 .text.bootloader_enable_wp
                0x4086f320        0xc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086f320                bootloader_enable_wp
 .text.bootloader_flash_get_spi_mode
                0x4086f32c       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4086f32c                bootloader_flash_get_spi_mode
 *libbootloader_support.a:bootloader_random.*(.literal .text .literal.* .text.*)
 .text.bootloader_fill_random
                0x4086f36a       0x7e esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                0x4086f36a                bootloader_fill_random
 *libbootloader_support.a:bootloader_random*.*(.literal.bootloader_random_disable .text.bootloader_random_disable)
 .text.bootloader_random_disable
                0x4086f3e8       0xd4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
                0x4086f3e8                bootloader_random_disable
 *libbootloader_support.a:bootloader_random*.*(.literal.bootloader_random_enable .text.bootloader_random_enable)
 .text.bootloader_random_enable
                0x4086f4bc      0x150 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
                0x4086f4bc                bootloader_random_enable
 *libbootloader_support.a:bootloader_efuse.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_utility.*(.literal .text .literal.* .text.*)
 .text.log_invalid_app_partition
                0x4086f60c       0x84 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.index_to_partition
                0x4086f690       0x46 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.try_load_partition
                0x4086f6d6       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.cache_ll_l1_enable_bus.constprop.0
                0x4086f71a       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.set_actual_ota_seq
                0x4086f74a       0xaa esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.load_image
                0x4086f7f4      0x172 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_common_read_otadata
                0x4086f966       0xa2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4086f966                bootloader_common_read_otadata
 .text.bootloader_utility_load_partition_table
                0x4086fa08      0x234 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4086fa08                bootloader_utility_load_partition_table
 .text.bootloader_utility_get_selected_boot_partition
                0x4086fc3c      0x112 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4086fc3c                bootloader_utility_get_selected_boot_partition
 .text.bootloader_reset
                0x4086fd4e       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4086fd4e                bootloader_reset
 .text.bootloader_utility_load_boot_image
                0x4086fd6a      0x11c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4086fd6a                bootloader_utility_load_boot_image
 .text.bootloader_debug_buffer
                0x4086fe86        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4086fe86                bootloader_debug_buffer
 *libbootloader_support.a:bootloader_sha.*(.literal .text .literal.* .text.*)
 .text.bootloader_sha256_start
                0x4086fe88       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x4086fe88                bootloader_sha256_start
 .text.bootloader_sha256_data
                0x4086feb4       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x4086feb4                bootloader_sha256_data
 .text.bootloader_sha256_finish
                0x4086fee8       0x46 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x4086fee8                bootloader_sha256_finish
 *libbootloader_support.a:bootloader_console_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_console_deinit
                0x4086ff2e        0xa esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                0x4086ff2e                bootloader_console_deinit
 *libbootloader_support.a:bootloader_panic.*(.literal .text .literal.* .text.*)
 .text.__assert_func
                0x4086ff38       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                0x4086ff38                __assert_func
 .text.unlikely.abort
                0x4086ff58       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                0x4086ff58                abort
 *libbootloader_support.a:bootloader_soc.*(.literal .text .literal.* .text.*)
 .text.bootloader_ana_super_wdt_reset_config
                0x4086ff82       0x32 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x4086ff82                bootloader_ana_super_wdt_reset_config
 .text.bootloader_ana_bod_reset_config
                0x4086ffb4       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x4086ffb4                bootloader_ana_bod_reset_config
 .text.bootloader_ana_clock_glitch_reset_config
                0x4086ffde        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x4086ffde                bootloader_ana_clock_glitch_reset_config
 *libbootloader_support.a:esp_image_format.*(.literal .text .literal.* .text.*)
 .text.should_load
                0x4086ffe0       0x50 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_util_regions_overlap
                0x40870030       0x4e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.process_segments
                0x4087007e      0x3ea esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.image_load
                0x40870468      0x3ee esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_load_image
                0x40870856        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                0x40870856                bootloader_load_image
 *libbootloader_support.a:flash_encrypt.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_encryption_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_partitions.*(.literal .text .literal.* .text.*)
 .text.esp_partition_table_verify
                0x4087085e      0x17e esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                0x4087085e                esp_partition_table_verify
 *libbootloader_support.a:secure_boot.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_signatures_bootloader.*(.literal .text .literal.* .text.*)
 *libmicro-ecc.a:*.*(.literal .text .literal.* .text.*)
 *libspi_flash.a:*.*(.literal .text .literal.* .text.*)
 *libhal.a:wdt_hal_iram.*(.literal .text .literal.* .text.*)
 *libhal.a:mmu_hal.*(.literal .text .literal.* .text.*)
 .text.mmu_ll_check_valid_paddr_region.isra.0
                0x408709dc       0x8c esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_unmap_all
                0x40870a68       0x24 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x40870a68                mmu_hal_unmap_all
 .text.mmu_hal_init
                0x40870a8c       0x20 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x40870a8c                mmu_hal_init
 .text.mmu_hal_pages_to_bytes
                0x40870aac       0x32 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x40870aac                mmu_hal_pages_to_bytes
 .text.mmu_hal_check_valid_ext_vaddr_region
                0x40870ade       0x20 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x40870ade                mmu_hal_check_valid_ext_vaddr_region
 .text.mmu_hal_map_region
                0x40870afe      0x10c esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x40870afe                mmu_hal_map_region
 *libhal.a:cache_hal.*(.literal .text .literal.* .text.*)
 .text.s_cache_hal_init_ctx
                0x40870c0a       0x32 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x40870c0a                s_cache_hal_init_ctx
 .text.cache_hal_init
                0x40870c3c       0x48 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x40870c3c                cache_hal_init
 .text.s_update_cache_state
                0x40870c84       0x34 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x40870c84                s_update_cache_state
 .text.cache_hal_disable
                0x40870cb8       0x24 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x40870cb8                cache_hal_disable
 .text.cache_hal_enable
                0x40870cdc       0x2c esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x40870cdc                cache_hal_enable
 *libhal.a:efuse_hal.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:rtc_clk.*(.literal .text .literal.* .text.*)
 .text.rtc_clk_bbpll_disable
                0x40870d08       0x22 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_8m
                0x40870d2a       0x48 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_xtal
                0x40870d72       0x5a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_pll_mhz
                0x40870dcc       0x82 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enable.part.0
                0x40870e4e       0x54 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enable
                0x40870ea2       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870ea2                rtc_clk_32k_enable
 .text.rtc_clk_32k_enable_external
                0x40870ebc       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870ebc                rtc_clk_32k_enable_external
 .text.rtc_clk_rc32k_enable
                0x40870ed6       0x2c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870ed6                rtc_clk_rc32k_enable
 .text.rtc_clk_8m_enable
                0x40870f02       0x2c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870f02                rtc_clk_8m_enable
 .text.rtc_clk_slow_src_set
                0x40870f2e       0x50 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870f2e                rtc_clk_slow_src_set
 .text.rtc_clk_slow_src_get
                0x40870f7e       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870f7e                rtc_clk_slow_src_get
 .text.rtc_clk_slow_freq_get_hz
                0x40870f9e       0x24 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870f9e                rtc_clk_slow_freq_get_hz
 .text.rtc_clk_fast_src_set
                0x40870fc2       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870fc2                rtc_clk_fast_src_set
 .text.rtc_clk_set_cpu_switch_to_pll
                0x40870ff2        0x2 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870ff2                rtc_clk_set_cpu_switch_to_pll
 .text.rtc_clk_xtal_freq_get
                0x40870ff4       0x74 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40870ff4                rtc_clk_xtal_freq_get
                0x40870ff4                rtc_get_xtal
 .text.rtc_clk_cpu_freq_mhz_to_config
                0x40871068       0x6a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40871068                rtc_clk_cpu_freq_mhz_to_config
 .text.rtc_clk_cpu_freq_set_config
                0x408710d2      0x18a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x408710d2                rtc_clk_cpu_freq_set_config
 .text.rtc_clk_cpu_freq_get_config
                0x4087125c       0xfa esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4087125c                rtc_clk_cpu_freq_get_config
 .text.rtc_clk_xtal_freq_update
                0x40871356       0x26 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x40871356                rtc_clk_xtal_freq_update
 .text.rtc_clk_apb_freq_get
                0x4087137c       0xec esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4087137c                rtc_clk_apb_freq_get
 *libesp_hw_support.a:rtc_time.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:regi2c_ctrl.*(.literal .text .literal.* .text.*)
 *libefuse.a:*.*(.literal .text .literal.* .text.*)
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x40871468                        _loader_text_end = ABSOLUTE (.)

.iram.text      0x4086c110        0x0
                0x4086c110                        . = ALIGN (0x10)
 *(.entry.text)
 *(.init.literal)
 *(.init)

.dram0.bss      0x40875610      0x110
                0x40875610                        . = ALIGN (0x8)
                0x40875610                        _dram_start = ABSOLUTE (.)
                0x40875610                        _bss_start = ABSOLUTE (.)
 *(.dynsbss)
 *(.sbss)
 *(.sbss.*)
 .sbss.ota_has_initial_contents
                0x40875610        0x1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 *fill*         0x40875611        0x3 
 .sbss.ram_obfs_value
                0x40875614        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .sbss.mapped   0x4087561c        0x1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x4087561d        0x3 
 .sbss.s_bbpll_digi_consumers_ref_count
                0x40875620        0x4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .sbss.s_cur_pll_freq
                0x40875624        0x4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .sbss.ctx      0x40875628        0x8 esp-idf/hal/libhal.a(cache_hal.c.obj)
 *(.gnu.linkonce.sb.*)
 *(.scommon)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(.dynbss)
 *(.bss)
 *(.bss.*)
 .bss.ctx       0x40875630       0xd8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .bss.bootloader_image_hdr
                0x40875708       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x40875708                bootloader_image_hdr
 *(.gnu.linkonce.b.*)
 *(COMMON)
                0x40875720                        . = ALIGN (0x8)
                0x40875720                        _bss_end = ABSOLUTE (.)

.dram0.bootdesc
                0x40875720       0x50
                0x40875720                        _data_start = ABSOLUTE (.)
 *(.data_bootloader_desc .data_bootloader_desc.*)
 .data_bootloader_desc
                0x40875720       0x50 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                0x40875720                esp_bootloader_desc

.dram0.data     0x40875770        0x8
 *(.dram1 .dram1.*)
 .dram1.0       0x40875770        0x4 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 *(.data)
 *(.data.*)
 *(.gnu.linkonce.d.*)
 *(.data1)
 *(.sdata)
 *(.sdata.*)
 .sdata.current_read_mapping
                0x40875774        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *(.gnu.linkonce.s.*)
 *(.gnu.linkonce.s2.*)
 *(.jcr)
                0x40875778                        _data_end = ABSOLUTE (.)

.dram0.rodata   0x40875778     0x14d0
                0x40875778                        _rodata_start = ABSOLUTE (.)
 *(.rodata)
 *(.rodata.*)
 .rodata.__assert_func.str1.4
                0x40875778     0x13a5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                 0x22 (size before relaxing)
 .rodata.abort.str1.4
                0x40876b1d       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .rodata.call_start_cpu0.str1.4
                0x40876b1d       0x31 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .rodata.log_invalid_app_partition.str1.4
                0x40876b1d       0x99 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.try_load_partition.str1.4
                0x40876b1d       0x37 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.set_actual_ota_seq.str1.4
                0x40876b1d       0x6e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.load_image.str1.4
                0x40876b1d       0x93 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_common_read_otadata.str1.4
                0x40876b1d       0x7e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_load_partition_table.str1.4
                0x40876b1d      0x198 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_get_selected_boot_partition.str1.4
                0x40876b1d       0xec esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_load_boot_image.str1.4
                0x40876b1d       0xc3 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 *fill*         0x40876b1d        0x3 
 .rodata.__func__.0
                0x40876b20       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.esp_partition_table_verify.str1.4
                0x40876b30      0x131 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .rodata.bootloader_util_regions_overlap.str1.4
                0x40876b30       0x5e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.process_segments.str1.4
                0x40876b30      0x246 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.image_load.str1.4
                0x40876b30      0x192 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.__func__.0
                0x40876b30       0x20 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.__func__.1
                0x40876b50       0x16 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.bootloader_sha256_data.str1.4
                0x40876b66       0x51 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 *fill*         0x40876b66        0x2 
 .rodata.__func__.0
                0x40876b68       0x19 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 *fill*         0x40876b81        0x3 
 .rodata.__func__.1
                0x40876b84       0x17 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .rodata.bootloader_ana_super_wdt_reset_config.str1.4
                0x40876b9b       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 *fill*         0x40876b9b        0x1 
 .rodata.__func__.0
                0x40876b9c       0x26 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .rodata.bootloader_init.str1.4
                0x40876bc2       0xf8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 *fill*         0x40876bc2        0x2 
 .rodata.__func__.0
                0x40876bc4       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .rodata.bootloader_common_check_chip_validity.str1.4
                0x40876bd4       0xcb esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .rodata.bootloader_fill_random.str1.4
                0x40876bd4       0x4c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .rodata.__func__.0
                0x40876bd4       0x17 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .rodata.bootloader_mmap.str1.4
                0x40876beb       0x8d esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_flash_read.str1.4
                0x40876beb       0xc4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.str1.4
                0x40876beb       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_flash_write.str1.4
                0x40876beb       0xcc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x40876beb        0x1 
 .rodata.__func__.0
                0x40876bec       0x1b esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x40876c07        0x1 
 .rodata.__func__.1
                0x40876c08       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_init_spi_flash.str1.4
                0x40876c30       0xc9 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .rodata.bootloader_read_bootloader_header.str1.4
                0x40876c30       0x3d esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_check_bootloader_validity.str1.4
                0x40876c30       0x23 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_enable_random.str1.4
                0x40876c30       0x32 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_print_banner.str1.4
                0x40876c30       0x4d esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.rtc_clk_init.str1.4
                0x40876c30       0x39 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .rodata.rtc_clk_xtal_freq_get.str1.4
                0x40876c30       0x43 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.rtc_clk_cpu_freq_get_config.str1.4
                0x40876c30       0x31 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.rtc_clk_apb_freq_get.str1.4
                0x40876c30       0x22 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.str1.4
                0x40876c30       0x4d esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .rodata.__func__.1
                0x40876c30       0x17 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 *(.gnu.linkonce.r.*)
 *(.rodata1)
 *(.sdata2 .sdata2.* .srodata .srodata.*)
                0x40876c47                        __XT_EXCEPTION_TABLE_ = ABSOLUTE (.)
 *(.xt_except_table)
 *(.gcc_except_table)
 *(.gnu.linkonce.e.*)
 *(.gnu.version_r)
 *(.eh_frame_hdr)
 *(.eh_frame)
                0x40876cfc                        . = ((. + 0x3) & 0xfffffffffffffffc)
 *fill*         0x40876c47        0x1 
                0x40876c48                        __init_array_start = ABSOLUTE (.)
 *crtbegin.*(.ctors)
 *(EXCLUDE_FILE(*crtend.*) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)
                0x40876c48                        __init_array_end = ABSOLUTE (.)
 *crtbegin.*(.dtors)
 *(EXCLUDE_FILE(*crtend.*) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
                0x40876c48                        __XT_EXCEPTION_DESCS_ = ABSOLUTE (.)
 *(.xt_except_desc)
 *(.gnu.linkonce.h.*)
                0x40876c48                        __XT_EXCEPTION_DESCS_END__ = ABSOLUTE (.)
 *(.xt_except_desc_end)
 *(.dynamic)
 *(.gnu.version_d)
                0x40876c48                        _rodata_end = ABSOLUTE (.)
                0x40876c48                        _lit4_start = ABSOLUTE (.)
 *(*.lit4)
 *(.lit4.*)
 *(.gnu.linkonce.lit4.*)
                0x40876c48                        _lit4_end = ABSOLUTE (.)
                0x40876c48                        . = ALIGN (0x4)
                0x40876c48                        _dram_end = ABSOLUTE (.)

.iram.text      0x4086c110      0xe2a
                0x4086c110                        _stext = .
                0x4086c110                        _text_start = ABSOLUTE (.)
 *(.literal .text .literal.* .text.* .stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 .text.esp_bootloader_get_description
                0x4086c110        0xa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                0x4086c110                esp_bootloader_get_description
 .text.call_start_cpu0
                0x4086c11a       0x82 esp-idf/main/libmain.a(bootloader_start.c.obj)
                0x4086c11a                call_start_cpu0
 .text.bootloader_init
                0x4086c19c      0x1b4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
                0x4086c19c                bootloader_init
 .text.bootloader_clock_configure
                0x4086c350      0x114 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                0x4086c350                bootloader_clock_configure
 .text.bootloader_init_mem
                0x4086c464        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                0x4086c464                bootloader_init_mem
 .text.bootloader_flash_update_id
                0x4086c466       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                0x4086c466                bootloader_flash_update_id
 .text.bootloader_init_spi_flash
                0x4086c482      0x1be esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                0x4086c482                bootloader_init_spi_flash
 .text.bootloader_clear_bss_section
                0x4086c640       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4086c640                bootloader_clear_bss_section
 .text.bootloader_read_bootloader_header
                0x4086c662       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4086c662                bootloader_read_bootloader_header
 .text.bootloader_check_bootloader_validity
                0x4086c6a0       0x54 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4086c6a0                bootloader_check_bootloader_validity
 .text.bootloader_config_wdt
                0x4086c6f4       0xc2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4086c6f4                bootloader_config_wdt
 .text.bootloader_enable_random
                0x4086c7b6       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4086c7b6                bootloader_enable_random
 .text.bootloader_print_banner
                0x4086c7e0       0x54 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4086c7e0                bootloader_print_banner
 .text.bootloader_console_init
                0x4086c834       0xde esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                0x4086c834                bootloader_console_init
 .text.esp_cpu_configure_region_protection
                0x4086c912      0x194 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                0x4086c912                esp_cpu_configure_region_protection
 .text.rtc_clk_init
                0x4086caa6      0x230 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                0x4086caa6                rtc_clk_init
 .text.get_act_hp_dbias
                0x4086ccd6       0x34 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
                0x4086ccd6                get_act_hp_dbias
 .text.get_act_lp_dbias
                0x4086cd0a       0x34 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
                0x4086cd0a                get_act_lp_dbias
 .text.wdt_hal_init
                0x4086cd3e      0x1ec esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                0x4086cd3e                wdt_hal_init
 *(.iram .iram.*)
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x4086cf3a                        . = (. + 0x10)
 *fill*         0x4086cf2a       0x10 
                0x4086cf3a                        _text_end = ABSOLUTE (.)
                0x4086cf3a                        _etext = .

.riscv.attributes
                0x00000000       0x4d
 *(.riscv.attributes)
 .riscv.attributes
                0x00000000       0x49 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .riscv.attributes
                0x00000049       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .riscv.attributes
                0x00000092       0x49 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .riscv.attributes
                0x000000db       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .riscv.attributes
                0x00000124       0x49 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .riscv.attributes
                0x0000016d       0x49 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .riscv.attributes
                0x000001b6       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .riscv.attributes
                0x000001ff       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .riscv.attributes
                0x00000248       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .riscv.attributes
                0x00000291       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .riscv.attributes
                0x000002da       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .riscv.attributes
                0x00000323       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .riscv.attributes
                0x0000036c       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .riscv.attributes
                0x000003b5       0x4d esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .riscv.attributes
                0x00000402       0x49 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .riscv.attributes
                0x0000044b       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .riscv.attributes
                0x00000494       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .riscv.attributes
                0x000004dd       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .riscv.attributes
                0x00000526       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .riscv.attributes
                0x0000056f       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .riscv.attributes
                0x000005b8       0x4d esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .riscv.attributes
                0x00000605       0x4d esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .riscv.attributes
                0x00000652       0x49 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .riscv.attributes
                0x0000069b       0x49 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .riscv.attributes
                0x000006e4       0x49 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .riscv.attributes
                0x0000072d       0x49 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .riscv.attributes
                0x00000776       0x4d esp-idf/log/liblog.a(log_noos.c.obj)
 .riscv.attributes
                0x000007c3       0x49 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .riscv.attributes
                0x0000080c       0x49 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .riscv.attributes
                0x00000855       0x49 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .riscv.attributes
                0x0000089e       0x49 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .riscv.attributes
                0x000008e7       0x49 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug
 *(.debug)

.line
 *(.line)

.debug_srcinfo
 *(.debug_srcinfo)

.debug_sfnames
 *(.debug_sfnames)

.debug_aranges  0x00000000      0x8d0
 *(.debug_aranges)
 .debug_aranges
                0x00000000       0x20 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_aranges
                0x00000020       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_aranges
                0x00000048       0x28 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_aranges
                0x00000070       0x98 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_aranges
                0x00000108       0x20 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_aranges
                0x00000128       0x70 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_aranges
                0x00000198       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_aranges
                0x000001b8       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_aranges
                0x000001e8       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_aranges
                0x00000218       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .debug_aranges
                0x00000238       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_aranges
                0x00000280       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_aranges
                0x000002a0       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_aranges
                0x000002c0       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_aranges
                0x000002e0       0x40 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_aranges
                0x00000320       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .debug_aranges
                0x00000348       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_aranges
                0x00000400       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .debug_aranges
                0x00000448       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_aranges
                0x00000490       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_aranges
                0x000004b0       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_aranges
                0x000004d0       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_aranges
                0x000004f0      0x120 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_aranges
                0x00000610       0x60 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .debug_aranges
                0x00000670       0x40 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .debug_aranges
                0x000006b0       0x28 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_aranges
                0x000006d8       0x38 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_aranges
                0x00000710       0x40 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_aranges
                0x00000750       0x58 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_aranges
                0x000007a8       0x38 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_aranges
                0x000007e0       0x68 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_aranges
                0x00000848       0x88 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_pubnames
 *(.debug_pubnames)

.debug_info     0x00000000    0x3b5b5
 *(.debug_info .gnu.linkonce.wi.*)
 .debug_info    0x00000000      0x181 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_info    0x00000181      0x21d esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_info    0x0000039e      0xc8f esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_info    0x0000102d     0x1f7a esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_info    0x00002fa7      0x5ae esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_info    0x00003555     0x23bd esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_info    0x00005912       0xc6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_info    0x000059d8      0x44e esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_info    0x00005e26      0x184 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_info    0x00005faa     0x37a8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .debug_info    0x00009752      0x9ea esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_info    0x0000a13c      0x33c esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_info    0x0000a478       0xa6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_info    0x0000a51e      0x2b2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_info    0x0000a7d0      0x987 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_info    0x0000b157      0x42e esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .debug_info    0x0000b585     0x5392 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_info    0x00010917      0xbd6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .debug_info    0x000114ed     0x1f40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_info    0x0001342d     0x4aa4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_info    0x00017ed1      0x387 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_info    0x00018258     0x6e6c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_info    0x0001f0c4     0x57fc esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_info    0x000248c0     0x48d0 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .debug_info    0x00029190      0x425 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .debug_info    0x000295b5     0x4cef esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_info    0x0002e2a4      0x281 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_info    0x0002e525     0x37f7 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_info    0x00031d1c     0x3a97 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_info    0x000357b3      0x999 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_info    0x0003614c     0x4581 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_info    0x0003a6cd      0xee8 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_abbrev   0x00000000     0x55f9
 *(.debug_abbrev)
 .debug_abbrev  0x00000000       0xaa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_abbrev  0x000000aa       0xfb esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_abbrev  0x000001a5      0x309 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_abbrev  0x000004ae      0x535 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_abbrev  0x000009e3      0x1fd esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_abbrev  0x00000be0      0x4fd esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_abbrev  0x000010dd       0x89 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_abbrev  0x00001166      0x1ca esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_abbrev  0x00001330       0xdc esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_abbrev  0x0000140c      0x37c esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .debug_abbrev  0x00001788      0x296 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_abbrev  0x00001a1e      0x143 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_abbrev  0x00001b61       0x65 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_abbrev  0x00001bc6      0x194 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_abbrev  0x00001d5a      0x322 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_abbrev  0x0000207c       0xd8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .debug_abbrev  0x00002154      0x579 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_abbrev  0x000026cd      0x2fe esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .debug_abbrev  0x000029cb      0x31c esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_abbrev  0x00002ce7      0x370 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_abbrev  0x00003057      0x15f esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_abbrev  0x000031b6      0x4d4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_abbrev  0x0000368a      0x62f esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_abbrev  0x00003cb9      0x2fd esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .debug_abbrev  0x00003fb6      0x154 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .debug_abbrev  0x0000410a      0x364 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_abbrev  0x0000446e      0x1a5 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_abbrev  0x00004613      0x216 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_abbrev  0x00004829      0x374 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_abbrev  0x00004b9d      0x28d esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_abbrev  0x00004e2a      0x435 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_abbrev  0x0000525f      0x39a esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_line     0x00000000    0x13df7
 *(.debug_line)
 .debug_line    0x00000000      0x202 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_line    0x00000202      0x3ee esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_line    0x000005f0      0x63f esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_line    0x00000c2f     0x1df5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_line    0x00002a24      0x6ab esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_line    0x000030cf     0x1f59 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_line    0x00005028      0x1e0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_line    0x00005208      0x431 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_line    0x00005639      0x2d2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_line    0x0000590b      0xa12 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .debug_line    0x0000631d      0x827 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_line    0x00006b44      0x4d6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_line    0x0000701a       0xeb esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_line    0x00007105      0x4c0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_line    0x000075c5      0xc01 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_line    0x000081c6      0x482 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .debug_line    0x00008648     0x19c1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_line    0x0000a009      0xa00 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .debug_line    0x0000aa09      0xa7a esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_line    0x0000b483      0x575 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_line    0x0000b9f8      0x724 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_line    0x0000c11c      0xb60 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_line    0x0000cc7c     0x200a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_line    0x0000ec86      0x881 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .debug_line    0x0000f507      0x76a esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .debug_line    0x0000fc71      0xd24 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_line    0x00010995      0x423 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_line    0x00010db8      0x454 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_line    0x0001120c      0x6ff esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_line    0x0001190b      0x443 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_line    0x00011d4e     0x14a8 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_line    0x000131f6      0xc01 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_frame    0x00000000     0x1774
 *(.debug_frame)
 .debug_frame   0x00000000       0x20 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_frame   0x00000020       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_frame   0x00000060       0x38 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_frame   0x00000098      0x284 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_frame   0x0000031c       0x58 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_frame   0x00000374      0x198 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_frame   0x0000050c       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_frame   0x0000052c       0x68 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_frame   0x00000594       0x4c esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_frame   0x000005e0       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .debug_frame   0x00000614       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_frame   0x000006dc       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_frame   0x0000070c       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_frame   0x0000072c       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_frame   0x00000770       0xc0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_frame   0x00000830       0x4c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .debug_frame   0x0000087c      0x2b8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_frame   0x00000b34       0xac esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .debug_frame   0x00000be0       0xbc esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_frame   0x00000c9c       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_frame   0x00000ccc       0x34 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_frame   0x00000d00       0x3c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_frame   0x00000d3c      0x31c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_frame   0x00001058      0x10c esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .debug_frame   0x00001164       0xd0 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .debug_frame   0x00001234       0x54 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_frame   0x00001288       0x84 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_frame   0x0000130c       0x70 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_frame   0x0000137c       0xb0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_frame   0x0000142c       0x60 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_frame   0x0000148c      0x170 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_frame   0x000015fc      0x178 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_str      0x00000000    0x104da
 *(.debug_str)
 .debug_str     0x00000000    0x104da esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                0x2c9 (size before relaxing)
 .debug_str     0x000104da      0x2df esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_str     0x000104da      0xa3e esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_str     0x000104da     0x18db esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_str     0x000104da      0x4b8 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_str     0x000104da     0x18f3 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_str     0x000104da      0x26d esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_str     0x000104da      0x3fa esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_str     0x000104da      0x2cd esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_str     0x000104da     0x2fe4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .debug_str     0x000104da     0x1069 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_str     0x000104da      0x6ab esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_str     0x000104da      0x25c esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_str     0x000104da      0x33c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_str     0x000104da      0x9ce esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_str     0x000104da      0x2f7 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .debug_str     0x000104da     0x3acf esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_str     0x000104da     0x145c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .debug_str     0x000104da     0x1c88 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_str     0x000104da     0x2958 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_str     0x000104da      0x331 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_str     0x000104da     0x4cf8 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_str     0x000104da     0x39ad esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_str     0x000104da     0x3356 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .debug_str     0x000104da      0x2eb esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .debug_str     0x000104da     0x2baf esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_str     0x000104da      0x2ff esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_str     0x000104da     0x28e9 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_str     0x000104da     0x2ae8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_str     0x000104da      0x5b8 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_str     0x000104da     0x2b9b esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_str     0x000104da      0x713 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_loc      0x00000000     0x90e1
 *(.debug_loc)
 .debug_loc     0x00000000       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_loc     0x000000c8       0xc7 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_loc     0x0000018f     0x117a esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_loc     0x00001309      0x1d9 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_loc     0x000014e2     0x200d esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_loc     0x000034ef      0x17d esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_loc     0x0000366c       0x32 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_loc     0x0000369e       0xb0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .debug_loc     0x0000374e      0x324 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_loc     0x00003a72      0x104 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_loc     0x00003b76      0x19c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_loc     0x00003d12      0x17c esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_loc     0x00003e8e     0x1185 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_loc     0x00005013      0x135 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .debug_loc     0x00005148       0xd2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_loc     0x0000521a       0x7b esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_loc     0x00005295       0x60 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_loc     0x000052f5      0x108 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_loc     0x000053fd      0xe27 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_loc     0x00006224      0x268 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .debug_loc     0x0000648c      0x702 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .debug_loc     0x00006b8e      0x50c esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_loc     0x0000709a      0x182 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_loc     0x0000721c      0x175 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_loc     0x00007391      0xf78 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_loc     0x00008309      0xdd8 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_macinfo
 *(.debug_macinfo)

.debug_pubtypes
 *(.debug_pubtypes)

.debug_ranges   0x00000000     0x1da0
 *(.debug_ranges)
 .debug_ranges  0x00000000       0x10 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_ranges  0x00000010       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_ranges  0x00000028       0x30 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_ranges  0x00000058      0x228 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_ranges  0x00000280       0x70 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_ranges  0x000002f0      0x380 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_ranges  0x00000670       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_ranges  0x00000680       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_ranges  0x000006a0       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_ranges  0x000006c0       0x80 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .debug_ranges  0x00000740       0x70 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_ranges  0x000007b0       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_ranges  0x000007c0       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_ranges  0x000007d0       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_ranges  0x00000810       0x90 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_ranges  0x000008a0       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .debug_ranges  0x000008b8      0x178 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_ranges  0x00000a30       0xa8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .debug_ranges  0x00000ad8       0x70 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_ranges  0x00000b48       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_ranges  0x00000b58       0x58 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_ranges  0x00000bb0      0x160 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_ranges  0x00000d10      0x630 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_ranges  0x00001340       0x68 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .debug_ranges  0x000013a8       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .debug_ranges  0x000013d8      0x288 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_ranges  0x00001660       0x60 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_ranges  0x000016c0       0x80 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_ranges  0x00001740       0x98 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_ranges  0x000017d8       0x88 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_ranges  0x00001860      0x410 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_ranges  0x00001c70      0x130 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_weaknames
 *(.debug_weaknames)

.debug_funcnames
 *(.debug_funcnames)

.debug_typenames
 *(.debug_typenames)

.debug_varnames
 *(.debug_varnames)

.debug_gnu_pubnames
 *(.debug_gnu_pubnames)

.debug_gnu_pubtypes
 *(.debug_gnu_pubtypes)

.debug_types
 *(.debug_types)

.debug_addr
 *(.debug_addr)

.debug_line_str
 *(.debug_line_str)

.debug_loclists
 *(.debug_loclists)

.debug_macro
 *(.debug_macro)

.debug_names
 *(.debug_names)

.debug_rnglists
 *(.debug_rnglists)

.debug_str_offsets
 *(.debug_str_offsets)

.comment        0x00000000       0x2f
 *(.comment)
 .comment       0x00000000       0x2f esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                 0x30 (size before relaxing)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .comment       0x0000002f       0x30 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .comment       0x0000002f       0x30 esp-idf/log/liblog.a(log_noos.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(cache_hal.c.obj)

.note.GNU-stack
 *(.note.GNU-stack)

/DISCARD/
 *(.rela.*)
OUTPUT(bootloader.elf elf32-littleriscv)

Cross Reference Table

Symbol                                            File
Cache_Disable_ICache                              esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Enable_ICache                               esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Freeze_ICache_Disable                       esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Freeze_ICache_Enable                        esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Get_ICache_Line_Size                        esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Invalidate_Addr                             esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Resume_ICache                               esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Suspend_ICache                              esp-idf/hal/libhal.a(cache_hal.c.obj)
EFUSE                                             esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
ESP_EFUSE_ACTIVE_HP_DBIAS                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ACTIVE_LP_DBIAS                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN0                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN1                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN2                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN3                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH0               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH1               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH2               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH3               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH4               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH5               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0_CH6               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN1                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN2                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN3                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_BLK_VERSION_MAJOR                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_BLK_VERSION_MINOR                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_CRYPT_DPA_ENABLE                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DBIAS_VOL_GAP                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_DIRECT_BOOT                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_ICACHE                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_MODE                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_DIS_FORCE_DOWNLOAD                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_ICACHE                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_PAD_JTAG                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_TWAI                                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_JTAG                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_USB_SERIAL_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_SERIAL_JTAG_ROM_PRINT           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DSLP_LP_DBG                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DSLP_LP_DBIAS                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_FLASH_CAP                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_TEMP                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_TPUW                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_VENDOR                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FORCE_SEND_RESUME                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_JTAG_SEL_ENABLE                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KEY0                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY1                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY2                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY3                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY4                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY5                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_0                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_1                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_2                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_3                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_4                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_5                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_LSLP_HP_DBG                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LSLP_HP_DBIAS                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_MAC                                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_MAC_EXT                                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_OCODE                                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_OPTIONAL_UNIQUE_ID                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PKG_VERSION                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_RD_DIS                                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY0                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY1                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY2                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY3                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY4                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY5                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_SYS_DATA2                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_BOOT_AGGRESSIVE_REVOKE           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_DISABLE_FAST_WAKE           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_EN                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE0                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE1                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE2                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_VERSION                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SEC_DPA_LEVEL                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SOFT_DIS_JTAG                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_BOOT_CRYPT_CNT                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_SPI_DOWNLOAD_MSPI_DIS                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SWAP_UART_SDIO_EN                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SYS_DATA_PART2                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_TEMP_CALIB                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_UART_PRINT_CONTROL                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_USB_EXCHG_PINS                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USER_DATA                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USER_DATA_MAC_CUSTOM                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_VDD_SPI_AS_GPIO                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MAJOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MINOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WDT_DELAY_SEL                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS                                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ACTIVE_HP_DBIAS                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ACTIVE_LP_DBIAS                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN0              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN1              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN2              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN3              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH0        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH1        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH2        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH3        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH4        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH5        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0_CH6        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN1            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN2            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN3            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLK1                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY0                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY1                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY2                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY3                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY4                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY5                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_SYS_DATA2                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_USR_DATA                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_CRYPT_DPA_ENABLE                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_CUSTOM_MAC                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DBIAS_VOL_GAP                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_ICACHE              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_FORCE_DOWNLOAD               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_ICACHE                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_WR_DIS_DIS_PAD_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_TWAI                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DSLP_LP_DBG                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DSLP_LP_DBIAS                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_CAP                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_TEMP                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_TPUW                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_VENDOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_JTAG_SEL_ENABLE                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_0                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_1                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_2                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_3                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_4                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_5                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_LSLP_HP_DBG                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LSLP_HP_DBIAS                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_MAC                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_MAC_EXT                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_OCODE                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_OPTIONAL_UNIQUE_ID               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PKG_VERSION                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_RD_DIS                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_EN                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE0          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE1          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE2          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_VERSION                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SEC_DPA_LEVEL                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SOFT_DIS_JTAG                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_WR_DIS_SPI_DOWNLOAD_MSPI_DIS            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SWAP_UART_SDIO_EN                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SYS_DATA_PART1                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_TEMP_CALIB                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_USB_EXCHG_PINS                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_VDD_SPI_AS_GPIO                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WDT_DELAY_SEL                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
LP_CLKRST                                         esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
LP_TIMER                                          esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
LP_UART                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
LP_WDT                                            esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
MODEM_LPCON                                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
MODEM_SYSCON                                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
PCR                                               esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
PMU                                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
ROM_Boot_Cache_Init                               esp-idf/hal/libhal.a(mmu_hal.c.obj)
SPIMEM0                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
SPIMEM1                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
TIMERG0                                           esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
TIMERG1                                           esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
UART0                                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
__ashldi3                                         D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__assert_func                                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                                  esp-idf/log/liblog.a(log_noos.c.obj)
                                                  esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
__clz_tab                                         D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
                                                  D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                                                  D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
__divdi3                                          D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
__getreent                                        esp-idf/main/libmain.a(bootloader_start.c.obj)
__lshrdi3                                         D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__popcountsi2                                     D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__sf                                              D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
__udivdi3                                         D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imac_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
_bss_end                                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
_bss_start                                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
_data_end                                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
_data_start                                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
_dram_end                                         esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_dram_start                                       esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_impure_data                                      D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
_impure_ptr                                       D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
abort                                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_after_init                             esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_ana_bod_reset_config                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_ana_clock_glitch_reset_config          esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_ana_super_wdt_reset_config             esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_atexit                                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_before_init                            esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_check_bootloader_validity              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_clear_bss_section                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_clock_configure                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_common_check_chip_validity             esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_common_get_active_otadata              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_get_partition_description       esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_crc                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_invalid              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_valid                esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_common_read_otadata                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_select_otadata                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_config_wdt                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_configure_spi_pins                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_console_deinit                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_console_init                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_debug_buffer                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_enable_random                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_enable_wp                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_execute_flash_command                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_fill_random                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_flash_clock_config                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_flash_cs_timing_config                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_flash_erase_range                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_erase_sector                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_flash_execute_command_common           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_get_spi_mode                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_flash_is_octal_mode_enabled            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_read                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_flash_read_sfdp                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_reset_chip                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_unlock                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_flash_update_id                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_flash_update_size                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_flash_write                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_flash_xmc_startup                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_image_hdr                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_init                                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_init_mem                               esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_init_spi_flash                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_load_image                             esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_load_image_no_verify                   esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_mmap                                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_mmap_get_free_pages                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_munmap                                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_print_banner                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_random_disable                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_random_enable                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
bootloader_read_bootloader_header                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
bootloader_read_flash_id                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
bootloader_reset                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_sha256_data                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_finish                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_flash_contents                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_hex_to_str                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_start                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_spi_flash_reset                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_utility_get_selected_boot_partition    esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_utility_load_boot_image                esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_utility_load_partition_table           esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
cache_hal_disable                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
cache_hal_enable                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
cache_hal_freeze                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_get_cache_line_size                     esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_init                                    esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
cache_hal_invalidate_addr                         esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_is_cache_enabled                        esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_resume                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_suspend                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_unfreeze                                esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_vaddr_to_cache_level_id                 esp-idf/hal/libhal.a(cache_hal.c.obj)
call_start_cpu0                                   esp-idf/main/libmain.a(bootloader_start.c.obj)
efuse_hal_blk_version                             esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
efuse_hal_chip_revision                           esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_clear_program_registers                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_flash_encryption_enabled                esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
efuse_hal_get_disable_wafer_version_major         esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_get_mac                                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_major_chip_version                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_minor_chip_version                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_is_coding_error_in_block                esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_program                                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_read                                    esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_rs_calculate                            esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_set_timing                              esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_bootloader_desc                               esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
esp_bootloader_get_description                    esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
esp_cpu_configure_region_protection               esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
esp_efuse_batch_write_begin                       esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_batch_write_cancel                      esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_batch_write_commit                      esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_block_is_empty                          esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_check_errors                            esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_count_unused_key_blocks                 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_destroy_block                           esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_disable_rom_download_mode               esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_enable_rom_secure_download_mode         esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_find_purpose                            esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_find_unused_key_block                   esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_coding_scheme                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_get_digest_revoke                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_field_size                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_get_key                                 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_key_dis_read                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_key_dis_write                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_key_purpose                         esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_keypurpose_dis_write                esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_pkg_ver                             esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_get_purpose_field                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_write_protect_of_digest_revoke      esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_key_block_unused                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_read_block                              esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_read_field_bit                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_field_blob                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_field_cnt                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_reg                                esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_set_digest_revoke                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_key_dis_read                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_set_key_dis_write                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_key_purpose                         esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_keypurpose_dis_write                esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_read_protect                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_rom_log_scheme                      esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_set_write_protect                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_write_protect_of_digest_revoke      esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_utility_apply_new_coding_scheme         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_burn_chip                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_burn_chip_opt                   esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_burn_efuses                     esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_check_errors                    esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_clear_program_registers         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_count_once                      esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_debug_dump_blocks               esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_debug_dump_pending              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_debug_dump_single_block         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_erase_virt_blocks               esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_fill_buff                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_get_number_of_items             esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_get_read_register_address       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_utility_is_correct_written_data         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_process                         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_read_reg                        esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_reset                           esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_update_virt_blocks              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_write_blob                      esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_write_cnt                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_write_reg                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_write_block                             esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_write_field_bit                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_write_field_blob                        esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_write_field_cnt                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_write_key                               esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_keys                              esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_reg                               esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_flash_encryption_cfg_verify_release_mode      esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_flash_encryption_enabled                      esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_flash_encryption_set_release_mode             esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_flash_write_protect_crypt_cnt                 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_get_flash_encryption_mode                     esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_image_get_flash_size                          esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_get_metadata                            esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify_bootloader                       esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify_bootloader_data                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_log_early_timestamp                           esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_lock                                 esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_lock_timeout                         esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_unlock                               esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_timestamp                                 esp-idf/log/liblog.a(log_noos.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
esp_partition_table_verify                        esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_crc32_le                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
esp_rom_delay_us                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_get_cpu_ticks_per_us                      esp-idf/log/liblog.a(log_noos.c.obj)
esp_rom_get_reset_reason                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
esp_rom_gpio_pad_set_drv                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
esp_rom_install_uart_printf                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
esp_rom_md5_final                                 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_md5_init                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_md5_update                                esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_output_flush_tx                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
esp_rom_output_tx_wait_idle                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
esp_rom_printf                                    esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
esp_rom_regi2c_read                               esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
esp_rom_regi2c_read_mask                          esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
esp_rom_regi2c_write                              esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_regi2c_write_mask                         esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c6.c.obj)
esp_rom_set_cpu_ticks_per_us                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_software_reset_system                     esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_spiflash_config_clk                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
esp_rom_spiflash_config_param                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
esp_rom_spiflash_erase_block                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_erase_sector                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_fix_dummylen                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
esp_rom_spiflash_read                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_wait_idle                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_write                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_write_encrypted                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_secure_boot_read_key_digests                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ets_efuse_clear_program_registers                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
ets_efuse_rs_calculate                            esp-idf/hal/libhal.a(efuse_hal.c.obj)
ets_sha_enable                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_finish                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_init                                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_update                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
get_act_hp_dbias                                  esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
get_act_lp_dbias                                  esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
lp_timer_hal_clear_alarm_intr_status              esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
lp_timer_hal_clear_overflow_intr_status           esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
lp_timer_hal_get_cycle_count                      esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
lp_timer_hal_set_alarm_target                     esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
memcmp                                            D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
memcpy                                            D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
memset                                            D:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imac_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
                                                  esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
mmu_hal_bytes_to_pages                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_check_valid_ext_vaddr_region              esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_init                                      esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c6.c.obj)
mmu_hal_map_region                                esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
mmu_hal_paddr_to_vaddr                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_pages_to_bytes                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_unmap_all                                 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
mmu_hal_unmap_region                              esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_vaddr_to_paddr                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
pmu_hp_system_analog_param_default                esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
pmu_hp_system_clock_param_default                 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
pmu_hp_system_digital_param_default               esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
pmu_hp_system_power_param_default                 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
pmu_hp_system_retention_param_default             esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
pmu_lp_system_analog_param_default                esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
pmu_lp_system_power_param_default                 esp-idf/esp_hw_support/libesp_hw_support.a(pmu_param.c.obj)
range_read_addr_blocks                            esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
range_write_addr_blocks                           esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
regi2c_read_impl                                  esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
regi2c_read_mask_impl                             esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
regi2c_write_impl                                 esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
regi2c_write_mask_impl                            esp-idf/esp_rom/libesp_rom.a(esp_rom_hp_regi2c_esp32c6.c.obj)
rom_spiflash_legacy_data                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c6.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
rtc_clk_32k_bootstrap                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_32k_enable                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_32k_enable_external                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_32k_enabled                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_8m_enable                                 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_8m_enabled                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_apb_freq_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_bbpll_add_consumer                        esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_bbpll_remove_consumer                     esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_get_config                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_mhz_to_config                    esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_set_config                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_set_config_fast                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_set_xtal                         esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_to_pll_and_pll_lock_release      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_set_to_default_config                 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_fast_src_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_fast_src_set                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_init                                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_rc32k_enable                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_set_cpu_switch_to_pll                     esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_slow_freq_get_hz                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
rtc_clk_slow_src_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_slow_src_set                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_xtal_freq_get                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
rtc_clk_xtal_freq_update                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_dig_8m_enabled                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_dig_clk8m_disable                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_dig_clk8m_enable                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_get_xtal                                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
s_cache_hal_init_ctx                              esp-idf/hal/libhal.a(cache_hal.c.obj)
s_get_cache_state                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
s_revoke_table                                    esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
s_table                                           esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
s_update_cache_state                              esp-idf/hal/libhal.a(cache_hal.c.obj)
wdt_hal_config_stage                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_deinit                                    esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
wdt_hal_enable                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_init                                      esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_set_flashboot_en                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_write_protect_disable                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_write_protect_enable                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
