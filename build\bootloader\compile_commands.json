[{"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -o CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32c6.c.obj -c E:\\APPprj\\LinkPet\\linkpet-esp\\build\\bootloader\\project_elf_src_esp32c6.c", "file": "E:\\APPprj\\LinkPet\\linkpet-esp\\build\\bootloader\\project_elf_src_esp32c6.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\lldesc.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\lldesc.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\dport_access_common.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\dport_access_common.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\dport_access_common.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\interrupts.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\interrupts.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\interrupts.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\gpio_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\gpio_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\gpio_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\uart_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\uart_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\uart_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\adc_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\adc_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\adc_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\dedic_gpio_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\dedic_gpio_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\dedic_gpio_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\gdma_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\gdma_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\gdma_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\spi_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\spi_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\spi_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\ledc_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\ledc_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\ledc_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\pcnt_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\pcnt_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\pcnt_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\rmt_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\rmt_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\rmt_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\sdm_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\sdm_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\sdm_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\i2s_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\i2s_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\i2s_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\i2c_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\i2c_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\i2c_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\temperature_sensor_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\temperature_sensor_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\temperature_sensor_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\timer_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\timer_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\timer_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\parlio_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\parlio_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\parlio_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\mcpwm_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\mcpwm_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\mcpwm_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\mpi_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\mpi_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\mpi_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\twai_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\twai_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\twai_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\ieee802154_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\ieee802154_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\ieee802154_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\rtc_io_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\rtc_io_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\rtc_io_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\sdio_slave_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\sdio_slave_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\sdio_slave_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\system_retention_periph.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\system_retention_periph.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\soc\\esp32c6\\system_retention_periph.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\hal_utils.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\hal_utils.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\hal_utils.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\efuse_hal.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\efuse_hal.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\efuse_hal.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32c6\\efuse_hal.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\esp32c6\\efuse_hal.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\esp32c6\\efuse_hal.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\lp_timer_hal.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\lp_timer_hal.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\lp_timer_hal.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mmu_hal.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\mmu_hal.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\mmu_hal.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\cache_hal.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\cache_hal.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\hal\\cache_hal.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include/spi_flash -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\spi_flash_wrap.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\spi_flash\\spi_flash_wrap.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\spi_flash\\spi_flash_wrap.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_bootloader_format\\CMakeFiles\\__idf_esp_bootloader_format.dir\\esp_bootloader_desc.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_bootloader_format\\esp_bootloader_desc.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_bootloader_format\\esp_bootloader_desc.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_common.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_common.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_common_loader.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_common_loader.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_clock_init.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_clock_init.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_mem.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_mem.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_random.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_random.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_efuse.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_efuse.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\flash_encrypt.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\flash_encrypt.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\secure_boot.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\secure_boot.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32c6.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_random_esp32c6.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_random_esp32c6.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\flash_qio_mode.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash_config_esp32c6.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32c6.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32c6.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_utility.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_utility.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\flash_partitions.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\flash_partitions.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp_image_format.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp_image_format.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_init.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_init.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_clock_loader.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_console.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_console.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_console_loader.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_console_loader.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_sha.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp32c6\\bootloader_sha.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp32c6\\bootloader_sha.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_soc.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp32c6\\bootloader_soc.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp32c6\\bootloader_soc.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_esp32c6.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp32c6\\bootloader_esp32c6.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp32c6\\bootloader_esp32c6.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_ecdsa.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp32c6\\bootloader_ecdsa.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\esp32c6\\bootloader_ecdsa.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_bootloader_format/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_panic.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader_support\\src\\bootloader_panic.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_table.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\esp32c6\\esp_efuse_table.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\esp32c6\\esp_efuse_table.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_fields.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\esp32c6\\esp_efuse_fields.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\esp32c6\\esp_efuse_fields.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_rtc_calib.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\esp32c6\\esp_efuse_rtc_calib.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\esp32c6\\esp_efuse_rtc_calib.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_utility.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\esp32c6\\esp_efuse_utility.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\esp32c6\\esp_efuse_utility.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\src\\esp_efuse_api.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\src\\esp_efuse_api.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\src\\esp_efuse_fields.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\src\\esp_efuse_fields.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\src\\esp_efuse_utility.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\src\\esp_efuse_utility.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\efuse\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_system\\esp_err.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_system\\esp_err.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\cpu.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\cpu.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\esp_cpu_intr.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\esp_cpu_intr.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\esp_cpu_intr.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\esp_memory_utils.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\esp_memory_utils.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\esp_memory_utils.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\cpu_region_protect.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\cpu_region_protect.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\cpu_region_protect.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_clk_init.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\rtc_clk_init.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\rtc_clk_init.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_clk.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\rtc_clk.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\rtc_clk.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_param.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\pmu_param.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\pmu_param.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_init.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\pmu_init.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\pmu_init.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_sleep.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\pmu_sleep.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\pmu_sleep.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_time.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\rtc_time.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\rtc_time.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\chip_info.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\chip_info.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\chip_info.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/esp_private -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\ocode_init.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\ocode_init.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_hw_support\\port\\esp32c6\\ocode_init.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/efuse/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/spi_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_common\\src\\esp_err_to_name.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_common\\src\\esp_err_to_name.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_crc.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_crc.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_sys.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_sys.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_uart.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_uart.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_spiflash.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_spiflash.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_spiflash.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_efuse.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_efuse.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_efuse.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_hp_regi2c_esp32c6.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_hp_regi2c_esp32c6.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_hp_regi2c_esp32c6.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_systimer.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_systimer.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_systimer.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_wdt.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_wdt.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\esp_rom\\patches\\esp_rom_wdt.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\log\\log.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\log\\log.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_buffers.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\log\\log_buffers.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\log\\log_buffers.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/platform_port/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_noos.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\log\\log_noos.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\log\\log_noos.c"}, {"directory": "E:/APPprj/LinkPet/linkpet-esp/build/bootloader", "command": "D:\\Espressif\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.1-dirty\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IE:/APPprj/LinkPet/linkpet-esp/build/bootloader/config -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/log/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/include/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_rom/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_common/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/include/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/dma/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/ldo/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/. -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/esp_hw_support/port/esp32c6/private_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/newlib/platform_include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/riscv/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6 -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/soc/esp32c6/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/bootloader_flash/include -ID:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1/components/bootloader/subproject=. -fmacro-prefix-map=D:/Espressif/frameworks/esp-idf-v5.3.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj -c D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader\\subproject\\main\\bootloader_start.c", "file": "D:\\Espressif\\frameworks\\esp-idf-v5.3.1\\components\\bootloader\\subproject\\main\\bootloader_start.c"}]